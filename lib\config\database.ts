import mysql from 'mysql2/promise'
import { config } from 'dotenv'

// Load environment variables
config()

export interface DatabaseConfig {
  host: string
  port: number
  user: string
  password: string
  database: string
  connectionLimit: number
  acquireTimeout: number
  timeout: number
  reconnect: boolean
  charset: string
}

export const databaseConfig: DatabaseConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'jewellery_pro_db',
  connectionLimit: 10,
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true,
  charset: 'utf8mb4'
}

// Create connection pool
let pool: mysql.Pool | null = null

export function getPool(): mysql.Pool {
  if (!pool) {
    pool = mysql.createPool({
      ...databaseConfig,
      namedPlaceholders: true,
      supportBigNumbers: true,
      bigNumberStrings: true,
      dateStrings: false,
      debug: process.env.NODE_ENV === 'development',
      multipleStatements: false,
      timezone: '+00:00'
    })

    // Handle pool events
    pool.on('connection', (connection) => {
      console.log('New database connection established as id ' + connection.threadId)
    })

    pool.on('error', (err) => {
      console.error('Database pool error:', err)
      if (err.code === 'PROTOCOL_CONNECTION_LOST') {
        console.log('Recreating database pool...')
        pool = null
        getPool()
      }
    })
  }

  return pool
}

// Test database connection
export async function testConnection(): Promise<boolean> {
  try {
    const pool = getPool()
    const connection = await pool.getConnection()
    await connection.ping()
    connection.release()
    console.log('Database connection successful')
    return true
  } catch (error) {
    console.error('Database connection failed:', error)
    return false
  }
}

// Execute query with error handling
export async function executeQuery<T = any>(
  query: string,
  params: any[] = []
): Promise<T> {
  const pool = getPool()

  try {
    const [results] = await pool.execute(query, params)
    return results as T
  } catch (error) {
    console.error('Query execution error:', error)
    console.error('Query:', query)
    console.error('Params:', params)
    throw error
  }
}

// Execute DDL query (for CREATE TRIGGER, etc.) using regular query instead of prepared statement
export async function executeDDLQuery<T = any>(
  query: string
): Promise<T> {
  const pool = getPool()

  try {
    const [results] = await pool.query(query)
    return results as T
  } catch (error) {
    console.error('DDL Query execution error:', error)
    console.error('Query:', query)
    throw error
  }
}

// Execute transaction
export async function executeTransaction<T>(
  callback: (connection: mysql.PoolConnection) => Promise<T>
): Promise<T> {
  const pool = getPool()
  const connection = await pool.getConnection()
  
  try {
    await connection.beginTransaction()
    const result = await callback(connection)
    await connection.commit()
    return result
  } catch (error) {
    await connection.rollback()
    throw error
  } finally {
    connection.release()
  }
}

// Close all connections
export async function closePool(): Promise<void> {
  if (pool) {
    await pool.end()
    pool = null
    console.log('Database pool closed')
  }
}
