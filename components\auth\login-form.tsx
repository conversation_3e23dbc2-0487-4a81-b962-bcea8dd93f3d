'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { Eye, EyeOff, LogIn } from 'lucide-react'
import { User, LoginForm as LoginFormType } from '@/lib/types'
import { useToast } from '@/components/ui/simple-toast'

interface LoginFormProps {
  onSuccess: (user: User) => void
}

export default function LoginForm({ onSuccess }: LoginFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const { toast } = useToast()

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormType>()

  const onSubmit = async (data: LoginFormType) => {
    setIsLoading(true)

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: 'Login Successful',
          description: `Welcome back, ${result.data.user.first_name}!`,
        })
        onSuccess(result.data.user)
      } else {
        toast({
          title: 'Login Failed',
          description: result.error || 'Invalid credentials',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('Login error:', error)
      toast({
        title: 'Login Failed',
        description: 'An error occurred. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      {/* Email Field */}
      <div className="form-group">
        <label htmlFor="email" className="form-label">
          Email Address
        </label>
        <input
          id="email"
          type="email"
          className="form-input"
          placeholder="Enter your email"
          {...register('email', {
            required: 'Email is required',
            pattern: {
              value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
              message: 'Please enter a valid email address',
            },
          })}
        />
        {errors.email && (
          <p className="form-error">{errors.email.message}</p>
        )}
      </div>

      {/* Password Field */}
      <div className="form-group">
        <label htmlFor="password" className="form-label">
          Password
        </label>
        <div className="relative">
          <input
            id="password"
            type={showPassword ? 'text' : 'password'}
            className="form-input pr-10"
            placeholder="Enter your password"
            {...register('password', {
              required: 'Password is required',
              minLength: {
                value: 6,
                message: 'Password must be at least 6 characters',
              },
            })}
          />
          <button
            type="button"
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
            onClick={() => setShowPassword(!showPassword)}
          >
            {showPassword ? (
              <EyeOff className="h-4 w-4 text-muted-foreground" />
            ) : (
              <Eye className="h-4 w-4 text-muted-foreground" />
            )}
          </button>
        </div>
        {errors.password && (
          <p className="form-error">{errors.password.message}</p>
        )}
      </div>

      {/* Submit Button */}
      <button
        type="submit"
        disabled={isLoading}
        className="w-full btn btn-primary btn-md flex items-center justify-center gap-2"
      >
        {isLoading ? (
          <div className="spinner"></div>
        ) : (
          <>
            <LogIn className="h-4 w-4" />
            Sign In
          </>
        )}
      </button>

      {/* Quick Login Buttons */}
      <div className="space-y-2">
        <p className="text-xs text-muted-foreground text-center">Quick Login:</p>
        <div className="grid grid-cols-3 gap-2">
          <button
            type="button"
            className="btn btn-outline btn-sm text-xs"
            onClick={() => {
              const form = document.querySelector('form') as HTMLFormElement
              const emailInput = form.querySelector('#email') as HTMLInputElement
              const passwordInput = form.querySelector('#password') as HTMLInputElement
              emailInput.value = '<EMAIL>'
              passwordInput.value = 'admin123'
            }}
          >
            Admin
          </button>
          <button
            type="button"
            className="btn btn-outline btn-sm text-xs"
            onClick={() => {
              const form = document.querySelector('form') as HTMLFormElement
              const emailInput = form.querySelector('#email') as HTMLInputElement
              const passwordInput = form.querySelector('#password') as HTMLInputElement
              emailInput.value = '<EMAIL>'
              passwordInput.value = 'manager123'
            }}
          >
            Manager
          </button>
          <button
            type="button"
            className="btn btn-outline btn-sm text-xs"
            onClick={() => {
              const form = document.querySelector('form') as HTMLFormElement
              const emailInput = form.querySelector('#email') as HTMLInputElement
              const passwordInput = form.querySelector('#password') as HTMLInputElement
              emailInput.value = '<EMAIL>'
              passwordInput.value = 'staff123'
            }}
          >
            Staff
          </button>
        </div>
      </div>
    </form>
  )
}
