import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/config/database'

// GET - Fetch current metal rates
export async function GET(request: NextRequest) {
  try {
    // Try to get rates from database first
    const ratesQuery = `
      SELECT 
        metal_type, purity, rate_per_gram
      FROM metal_rates 
      WHERE effective_date <= CURDATE()
      ORDER BY effective_date DESC, created_at DESC
    `
    
    const rates = await executeQuery<any[]>(ratesQuery, [])

    // Get latest rate for each metal type and purity combination
    const latestRates = rates.reduce((acc: any, rate) => {
      const key = `${rate.metal_type.toLowerCase()}_${rate.purity.toLowerCase()}`
      if (!acc[key]) {
        acc[key] = rate.rate_per_gram
      }
      return acc
    }, {})

    // If no rates in database, return default rates
    if (Object.keys(latestRates).length === 0) {
      const defaultRates = {
        'gold_24k': 6800,
        'gold_22k': 6200,
        'gold_18k': 4800,
        'gold_14k': 3800,
        'silver_999': 85,
        'silver_925': 78
      }

      return NextResponse.json({
        success: true,
        data: defaultRates,
        message: 'Using default rates - please update metal rates'
      })
    }

    return NextResponse.json({
      success: true,
      data: latestRates
    })

  } catch (error) {
    console.error('Get current metal rates error:', error)
    
    // Return default rates on error
    const defaultRates = {
      'gold_24k': 6800,
      'gold_22k': 6200,
      'gold_18k': 4800,
      'gold_14k': 3800,
      'silver_999': 85,
      'silver_925': 78
    }

    return NextResponse.json({
      success: true,
      data: defaultRates,
      message: 'Using default rates - database error'
    })
  }
}
