/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/dotenv";
exports.ids = ["vendor-chunks/dotenv"];
exports.modules = {

/***/ "(rsc)/./node_modules/dotenv/lib/main.js":
/*!*****************************************!*\
  !*** ./node_modules/dotenv/lib/main.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const fs = __webpack_require__(/*! fs */ \"fs\")\nconst path = __webpack_require__(/*! path */ \"path\")\nconst os = __webpack_require__(/*! os */ \"os\")\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\")\nconst packageJson = __webpack_require__(/*! ../package.json */ \"(rsc)/./node_modules/dotenv/package.json\")\n\nconst version = packageJson.version\n\n// Array of tips to display randomly\nconst TIPS = [\n  '🔐 encrypt with Dotenvx: https://dotenvx.com',\n  '🔐 prevent committing .env to code: https://dotenvx.com/precommit',\n  '🔐 prevent building .env in docker: https://dotenvx.com/prebuild',\n  '📡 observe env with Radar: https://dotenvx.com/radar',\n  '📡 auto-backup env with Radar: https://dotenvx.com/radar',\n  '📡 version env with Radar: https://dotenvx.com/radar',\n  '🛠️  run anywhere with `dotenvx run -- yourcommand`',\n  '⚙️  specify custom .env file path with { path: \\'/custom/path/.env\\' }',\n  '⚙️  enable debug logging with { debug: true }',\n  '⚙️  override existing env vars with { override: true }',\n  '⚙️  suppress all logs with { quiet: true }',\n  '⚙️  write to custom object with { processEnv: myObject }',\n  '⚙️  load multiple .env files with { path: [\\'.env.local\\', \\'.env\\'] }'\n]\n\n// Get a random tip from the tips array\nfunction _getRandomTip () {\n  return TIPS[Math.floor(Math.random() * TIPS.length)]\n}\n\nfunction parseBoolean (value) {\n  if (typeof value === 'string') {\n    return !['false', '0', 'no', 'off', ''].includes(value.toLowerCase())\n  }\n  return Boolean(value)\n}\n\nfunction supportsAnsi () {\n  return process.stdout.isTTY // && process.env.TERM !== 'dumb'\n}\n\nfunction dim (text) {\n  return supportsAnsi() ? `\\x1b[2m${text}\\x1b[0m` : text\n}\n\nconst LINE = /(?:^|^)\\s*(?:export\\s+)?([\\w.-]+)(?:\\s*=\\s*?|:\\s+?)(\\s*'(?:\\\\'|[^'])*'|\\s*\"(?:\\\\\"|[^\"])*\"|\\s*`(?:\\\\`|[^`])*`|[^#\\r\\n]+)?\\s*(?:#.*)?(?:$|$)/mg\n\n// Parse src into an Object\nfunction parse (src) {\n  const obj = {}\n\n  // Convert buffer to string\n  let lines = src.toString()\n\n  // Convert line breaks to same format\n  lines = lines.replace(/\\r\\n?/mg, '\\n')\n\n  let match\n  while ((match = LINE.exec(lines)) != null) {\n    const key = match[1]\n\n    // Default undefined or null to empty string\n    let value = (match[2] || '')\n\n    // Remove whitespace\n    value = value.trim()\n\n    // Check if double quoted\n    const maybeQuote = value[0]\n\n    // Remove surrounding quotes\n    value = value.replace(/^(['\"`])([\\s\\S]*)\\1$/mg, '$2')\n\n    // Expand newlines if double quoted\n    if (maybeQuote === '\"') {\n      value = value.replace(/\\\\n/g, '\\n')\n      value = value.replace(/\\\\r/g, '\\r')\n    }\n\n    // Add to object\n    obj[key] = value\n  }\n\n  return obj\n}\n\nfunction _parseVault (options) {\n  options = options || {}\n\n  const vaultPath = _vaultPath(options)\n  options.path = vaultPath // parse .env.vault\n  const result = DotenvModule.configDotenv(options)\n  if (!result.parsed) {\n    const err = new Error(`MISSING_DATA: Cannot parse ${vaultPath} for an unknown reason`)\n    err.code = 'MISSING_DATA'\n    throw err\n  }\n\n  // handle scenario for comma separated keys - for use with key rotation\n  // example: DOTENV_KEY=\"dotenv://:<EMAIL>/vault/.env.vault?environment=prod,dotenv://:<EMAIL>/vault/.env.vault?environment=prod\"\n  const keys = _dotenvKey(options).split(',')\n  const length = keys.length\n\n  let decrypted\n  for (let i = 0; i < length; i++) {\n    try {\n      // Get full key\n      const key = keys[i].trim()\n\n      // Get instructions for decrypt\n      const attrs = _instructions(result, key)\n\n      // Decrypt\n      decrypted = DotenvModule.decrypt(attrs.ciphertext, attrs.key)\n\n      break\n    } catch (error) {\n      // last key\n      if (i + 1 >= length) {\n        throw error\n      }\n      // try next key\n    }\n  }\n\n  // Parse decrypted .env string\n  return DotenvModule.parse(decrypted)\n}\n\nfunction _warn (message) {\n  console.error(`[dotenv@${version}][WARN] ${message}`)\n}\n\nfunction _debug (message) {\n  console.log(`[dotenv@${version}][DEBUG] ${message}`)\n}\n\nfunction _log (message) {\n  console.log(`[dotenv@${version}] ${message}`)\n}\n\nfunction _dotenvKey (options) {\n  // prioritize developer directly setting options.DOTENV_KEY\n  if (options && options.DOTENV_KEY && options.DOTENV_KEY.length > 0) {\n    return options.DOTENV_KEY\n  }\n\n  // secondary infra already contains a DOTENV_KEY environment variable\n  if (process.env.DOTENV_KEY && process.env.DOTENV_KEY.length > 0) {\n    return process.env.DOTENV_KEY\n  }\n\n  // fallback to empty string\n  return ''\n}\n\nfunction _instructions (result, dotenvKey) {\n  // Parse DOTENV_KEY. Format is a URI\n  let uri\n  try {\n    uri = new URL(dotenvKey)\n  } catch (error) {\n    if (error.code === 'ERR_INVALID_URL') {\n      const err = new Error('INVALID_DOTENV_KEY: Wrong format. Must be in valid uri format like dotenv://:<EMAIL>/vault/.env.vault?environment=development')\n      err.code = 'INVALID_DOTENV_KEY'\n      throw err\n    }\n\n    throw error\n  }\n\n  // Get decrypt key\n  const key = uri.password\n  if (!key) {\n    const err = new Error('INVALID_DOTENV_KEY: Missing key part')\n    err.code = 'INVALID_DOTENV_KEY'\n    throw err\n  }\n\n  // Get environment\n  const environment = uri.searchParams.get('environment')\n  if (!environment) {\n    const err = new Error('INVALID_DOTENV_KEY: Missing environment part')\n    err.code = 'INVALID_DOTENV_KEY'\n    throw err\n  }\n\n  // Get ciphertext payload\n  const environmentKey = `DOTENV_VAULT_${environment.toUpperCase()}`\n  const ciphertext = result.parsed[environmentKey] // DOTENV_VAULT_PRODUCTION\n  if (!ciphertext) {\n    const err = new Error(`NOT_FOUND_DOTENV_ENVIRONMENT: Cannot locate environment ${environmentKey} in your .env.vault file.`)\n    err.code = 'NOT_FOUND_DOTENV_ENVIRONMENT'\n    throw err\n  }\n\n  return { ciphertext, key }\n}\n\nfunction _vaultPath (options) {\n  let possibleVaultPath = null\n\n  if (options && options.path && options.path.length > 0) {\n    if (Array.isArray(options.path)) {\n      for (const filepath of options.path) {\n        if (fs.existsSync(filepath)) {\n          possibleVaultPath = filepath.endsWith('.vault') ? filepath : `${filepath}.vault`\n        }\n      }\n    } else {\n      possibleVaultPath = options.path.endsWith('.vault') ? options.path : `${options.path}.vault`\n    }\n  } else {\n    possibleVaultPath = path.resolve(process.cwd(), '.env.vault')\n  }\n\n  if (fs.existsSync(possibleVaultPath)) {\n    return possibleVaultPath\n  }\n\n  return null\n}\n\nfunction _resolveHome (envPath) {\n  return envPath[0] === '~' ? path.join(os.homedir(), envPath.slice(1)) : envPath\n}\n\nfunction _configVault (options) {\n  const debug = parseBoolean(process.env.DOTENV_CONFIG_DEBUG || (options && options.debug))\n  const quiet = parseBoolean(process.env.DOTENV_CONFIG_QUIET || (options && options.quiet))\n\n  if (debug || !quiet) {\n    _log('Loading env from encrypted .env.vault')\n  }\n\n  const parsed = DotenvModule._parseVault(options)\n\n  let processEnv = process.env\n  if (options && options.processEnv != null) {\n    processEnv = options.processEnv\n  }\n\n  DotenvModule.populate(processEnv, parsed, options)\n\n  return { parsed }\n}\n\nfunction configDotenv (options) {\n  const dotenvPath = path.resolve(process.cwd(), '.env')\n  let encoding = 'utf8'\n  let processEnv = process.env\n  if (options && options.processEnv != null) {\n    processEnv = options.processEnv\n  }\n  let debug = parseBoolean(processEnv.DOTENV_CONFIG_DEBUG || (options && options.debug))\n  let quiet = parseBoolean(processEnv.DOTENV_CONFIG_QUIET || (options && options.quiet))\n\n  if (options && options.encoding) {\n    encoding = options.encoding\n  } else {\n    if (debug) {\n      _debug('No encoding is specified. UTF-8 is used by default')\n    }\n  }\n\n  let optionPaths = [dotenvPath] // default, look for .env\n  if (options && options.path) {\n    if (!Array.isArray(options.path)) {\n      optionPaths = [_resolveHome(options.path)]\n    } else {\n      optionPaths = [] // reset default\n      for (const filepath of options.path) {\n        optionPaths.push(_resolveHome(filepath))\n      }\n    }\n  }\n\n  // Build the parsed data in a temporary object (because we need to return it).  Once we have the final\n  // parsed data, we will combine it with process.env (or options.processEnv if provided).\n  let lastError\n  const parsedAll = {}\n  for (const path of optionPaths) {\n    try {\n      // Specifying an encoding returns a string instead of a buffer\n      const parsed = DotenvModule.parse(fs.readFileSync(path, { encoding }))\n\n      DotenvModule.populate(parsedAll, parsed, options)\n    } catch (e) {\n      if (debug) {\n        _debug(`Failed to load ${path} ${e.message}`)\n      }\n      lastError = e\n    }\n  }\n\n  const populated = DotenvModule.populate(processEnv, parsedAll, options)\n\n  // handle user settings DOTENV_CONFIG_ options inside .env file(s)\n  debug = parseBoolean(processEnv.DOTENV_CONFIG_DEBUG || debug)\n  quiet = parseBoolean(processEnv.DOTENV_CONFIG_QUIET || quiet)\n\n  if (debug || !quiet) {\n    const keysCount = Object.keys(populated).length\n    const shortPaths = []\n    for (const filePath of optionPaths) {\n      try {\n        const relative = path.relative(process.cwd(), filePath)\n        shortPaths.push(relative)\n      } catch (e) {\n        if (debug) {\n          _debug(`Failed to load ${filePath} ${e.message}`)\n        }\n        lastError = e\n      }\n    }\n\n    _log(`injecting env (${keysCount}) from ${shortPaths.join(',')} ${dim(`-- tip: ${_getRandomTip()}`)}`)\n  }\n\n  if (lastError) {\n    return { parsed: parsedAll, error: lastError }\n  } else {\n    return { parsed: parsedAll }\n  }\n}\n\n// Populates process.env from .env file\nfunction config (options) {\n  // fallback to original dotenv if DOTENV_KEY is not set\n  if (_dotenvKey(options).length === 0) {\n    return DotenvModule.configDotenv(options)\n  }\n\n  const vaultPath = _vaultPath(options)\n\n  // dotenvKey exists but .env.vault file does not exist\n  if (!vaultPath) {\n    _warn(`You set DOTENV_KEY but you are missing a .env.vault file at ${vaultPath}. Did you forget to build it?`)\n\n    return DotenvModule.configDotenv(options)\n  }\n\n  return DotenvModule._configVault(options)\n}\n\nfunction decrypt (encrypted, keyStr) {\n  const key = Buffer.from(keyStr.slice(-64), 'hex')\n  let ciphertext = Buffer.from(encrypted, 'base64')\n\n  const nonce = ciphertext.subarray(0, 12)\n  const authTag = ciphertext.subarray(-16)\n  ciphertext = ciphertext.subarray(12, -16)\n\n  try {\n    const aesgcm = crypto.createDecipheriv('aes-256-gcm', key, nonce)\n    aesgcm.setAuthTag(authTag)\n    return `${aesgcm.update(ciphertext)}${aesgcm.final()}`\n  } catch (error) {\n    const isRange = error instanceof RangeError\n    const invalidKeyLength = error.message === 'Invalid key length'\n    const decryptionFailed = error.message === 'Unsupported state or unable to authenticate data'\n\n    if (isRange || invalidKeyLength) {\n      const err = new Error('INVALID_DOTENV_KEY: It must be 64 characters long (or more)')\n      err.code = 'INVALID_DOTENV_KEY'\n      throw err\n    } else if (decryptionFailed) {\n      const err = new Error('DECRYPTION_FAILED: Please check your DOTENV_KEY')\n      err.code = 'DECRYPTION_FAILED'\n      throw err\n    } else {\n      throw error\n    }\n  }\n}\n\n// Populate process.env with parsed values\nfunction populate (processEnv, parsed, options = {}) {\n  const debug = Boolean(options && options.debug)\n  const override = Boolean(options && options.override)\n  const populated = {}\n\n  if (typeof parsed !== 'object') {\n    const err = new Error('OBJECT_REQUIRED: Please check the processEnv argument being passed to populate')\n    err.code = 'OBJECT_REQUIRED'\n    throw err\n  }\n\n  // Set process.env\n  for (const key of Object.keys(parsed)) {\n    if (Object.prototype.hasOwnProperty.call(processEnv, key)) {\n      if (override === true) {\n        processEnv[key] = parsed[key]\n        populated[key] = parsed[key]\n      }\n\n      if (debug) {\n        if (override === true) {\n          _debug(`\"${key}\" is already defined and WAS overwritten`)\n        } else {\n          _debug(`\"${key}\" is already defined and was NOT overwritten`)\n        }\n      }\n    } else {\n      processEnv[key] = parsed[key]\n      populated[key] = parsed[key]\n    }\n  }\n\n  return populated\n}\n\nconst DotenvModule = {\n  configDotenv,\n  _configVault,\n  _parseVault,\n  config,\n  decrypt,\n  parse,\n  populate\n}\n\nmodule.exports.configDotenv = DotenvModule.configDotenv\nmodule.exports._configVault = DotenvModule._configVault\nmodule.exports._parseVault = DotenvModule._parseVault\nmodule.exports.config = DotenvModule.config\nmodule.exports.decrypt = DotenvModule.decrypt\nmodule.exports.parse = DotenvModule.parse\nmodule.exports.populate = DotenvModule.populate\n\nmodule.exports = DotenvModule\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/dotenv/lib/main.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/dotenv/package.json":
/*!******************************************!*\
  !*** ./node_modules/dotenv/package.json ***!
  \******************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"name":"dotenv","version":"17.2.1","description":"Loads environment variables from .env file","main":"lib/main.js","types":"lib/main.d.ts","exports":{".":{"types":"./lib/main.d.ts","require":"./lib/main.js","default":"./lib/main.js"},"./config":"./config.js","./config.js":"./config.js","./lib/env-options":"./lib/env-options.js","./lib/env-options.js":"./lib/env-options.js","./lib/cli-options":"./lib/cli-options.js","./lib/cli-options.js":"./lib/cli-options.js","./package.json":"./package.json"},"scripts":{"dts-check":"tsc --project tests/types/tsconfig.json","lint":"standard","pretest":"npm run lint && npm run dts-check","test":"tap run --allow-empty-coverage --disable-coverage --timeout=60000","test:coverage":"tap run --show-full-coverage --timeout=60000 --coverage-report=text --coverage-report=lcov","prerelease":"npm test","release":"standard-version"},"repository":{"type":"git","url":"git://github.com/motdotla/dotenv.git"},"homepage":"https://github.com/motdotla/dotenv#readme","funding":"https://dotenvx.com","keywords":["dotenv","env",".env","environment","variables","config","settings"],"readmeFilename":"README.md","license":"BSD-2-Clause","devDependencies":{"@types/node":"^18.11.3","decache":"^4.6.2","sinon":"^14.0.1","standard":"^17.0.0","standard-version":"^9.5.0","tap":"^19.2.0","typescript":"^4.8.4"},"engines":{"node":">=12"},"browser":{"fs":false}}');

/***/ })

};
;