(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[685],{2489:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(9205).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},911:function(e){e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c"}},6249:function(e,t,n){"use strict";n.d(t,{aU:function(){return ed},x8:function(){return ef},dk:function(){return ec},zt:function(){return ea},fC:function(){return el},Dx:function(){return eu},l_:function(){return es}});var r,o=n(2265),i=n.t(o,2),a=n(4887);function s(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}var l=n(8575),u=n(7437);function c(e,t=[]){let n=[],r=()=>{let t=n.map(e=>o.createContext(e));return function(n){let r=n?.[e]||t;return o.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let i=o.createContext(r),a=n.length;n=[...n,r];let s=t=>{let{scope:n,children:r,...s}=t,l=n?.[e]?.[a]||i,c=o.useMemo(()=>s,Object.values(s));return(0,u.jsx)(l.Provider,{value:c,children:r})};return s.displayName=t+"Provider",[s,function(n,s){let l=s?.[e]?.[a]||i,u=o.useContext(l);if(u)return u;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return o.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}var d=n(7053),f=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,d.Z8)(`Primitive.${t}`),r=o.forwardRef((e,r)=>{let{asChild:o,...i}=e,a=o?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,u.jsx)(a,{...i,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function m(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}function v(e){let t=o.useRef(e);return o.useEffect(()=>{t.current=e}),o.useMemo(()=>(...e)=>t.current?.(...e),[])}var p="dismissableLayer.update",w=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),y=o.forwardRef((e,t)=>{var n,i;let{disableOutsidePointerEvents:a=!1,onEscapeKeyDown:c,onPointerDownOutside:d,onFocusOutside:m,onInteractOutside:y,onDismiss:h,...g}=e,T=o.useContext(w),[x,C]=o.useState(null),P=null!==(i=null==x?void 0:x.ownerDocument)&&void 0!==i?i:null===(n=globalThis)||void 0===n?void 0:n.document,[,N]=o.useState({}),R=(0,l.e)(t,e=>C(e)),S=Array.from(T.layers),[L]=[...T.layersWithOutsidePointerEventsDisabled].slice(-1),k=S.indexOf(L),D=x?S.indexOf(x):-1,M=T.layersWithOutsidePointerEventsDisabled.size>0,j=D>=k,A=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=v(e),i=o.useRef(!1),a=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){b("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",a.current),a.current=t,n.addEventListener("click",a.current,{once:!0})):t()}else n.removeEventListener("click",a.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",a.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...T.branches].some(e=>e.contains(t));!j||n||(null==d||d(e),null==y||y(e),e.defaultPrevented||null==h||h())},P),O=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=v(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&b("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;[...T.branches].some(e=>e.contains(t))||(null==m||m(e),null==y||y(e),e.defaultPrevented||null==h||h())},P);return!function(e,t=globalThis?.document){let n=v(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{D!==T.layers.size-1||(null==c||c(e),!e.defaultPrevented&&h&&(e.preventDefault(),h()))},P),o.useEffect(()=>{if(x)return a&&(0===T.layersWithOutsidePointerEventsDisabled.size&&(r=P.body.style.pointerEvents,P.body.style.pointerEvents="none"),T.layersWithOutsidePointerEventsDisabled.add(x)),T.layers.add(x),E(),()=>{a&&1===T.layersWithOutsidePointerEventsDisabled.size&&(P.body.style.pointerEvents=r)}},[x,P,a,T]),o.useEffect(()=>()=>{x&&(T.layers.delete(x),T.layersWithOutsidePointerEventsDisabled.delete(x),E())},[x,T]),o.useEffect(()=>{let e=()=>N({});return document.addEventListener(p,e),()=>document.removeEventListener(p,e)},[]),(0,u.jsx)(f.div,{...g,ref:R,style:{pointerEvents:M?j?"auto":"none":void 0,...e.style},onFocusCapture:s(e.onFocusCapture,O.onFocusCapture),onBlurCapture:s(e.onBlurCapture,O.onBlurCapture),onPointerDownCapture:s(e.onPointerDownCapture,A.onPointerDownCapture)})});y.displayName="DismissableLayer";var h=o.forwardRef((e,t)=>{let n=o.useContext(w),r=o.useRef(null),i=(0,l.e)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,u.jsx)(f.div,{...e,ref:i})});function E(){let e=new CustomEvent(p);document.dispatchEvent(e)}function b(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?m(i,a):i.dispatchEvent(a)}h.displayName="DismissableLayerBranch";var g=globalThis?.document?o.useLayoutEffect:()=>{},T=o.forwardRef((e,t)=>{var n,r;let{container:i,...s}=e,[l,c]=o.useState(!1);g(()=>c(!0),[]);let d=i||l&&(null===(r=globalThis)||void 0===r?void 0:null===(n=r.document)||void 0===n?void 0:n.body);return d?a.createPortal((0,u.jsx)(f.div,{...s,ref:t}),d):null});T.displayName="Portal";var x=e=>{var t,n;let r,i;let{present:a,children:s}=e,u=function(e){var t,n;let[r,i]=o.useState(),a=o.useRef(null),s=o.useRef(e),l=o.useRef("none"),[u,c]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},o.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return o.useEffect(()=>{let e=C(a.current);l.current="mounted"===u?e:"none"},[u]),g(()=>{let t=a.current,n=s.current;if(n!==e){let r=l.current,o=C(t);e?c("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):n&&r!==o?c("ANIMATION_OUT"):c("UNMOUNT"),s.current=e}},[e,c]),g(()=>{if(r){var e;let t;let n=null!==(e=r.ownerDocument.defaultView)&&void 0!==e?e:window,o=e=>{let o=C(a.current).includes(e.animationName);if(e.target===r&&o&&(c("ANIMATION_END"),!s.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},i=e=>{e.target===r&&(l.current=C(a.current))};return r.addEventListener("animationstart",i),r.addEventListener("animationcancel",o),r.addEventListener("animationend",o),()=>{n.clearTimeout(t),r.removeEventListener("animationstart",i),r.removeEventListener("animationcancel",o),r.removeEventListener("animationend",o)}}c("ANIMATION_END")},[r,c]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:o.useCallback(e=>{a.current=e?getComputedStyle(e):null,i(e)},[])}}(a),c="function"==typeof s?s({present:u.isPresent}):o.Children.only(s),d=(0,l.e)(u.ref,(r=null===(t=Object.getOwnPropertyDescriptor(c.props,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in r&&r.isReactWarning?c.ref:(r=null===(n=Object.getOwnPropertyDescriptor(c,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning?c.props.ref:c.props.ref||c.ref);return"function"==typeof s||u.isPresent?o.cloneElement(c,{ref:d}):null};function C(e){return(null==e?void 0:e.animationName)||"none"}x.displayName="Presence";var P=i[" useInsertionEffect ".trim().toString()]||g;Symbol("RADIX:SYNC_STATE");var N=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),R=o.forwardRef((e,t)=>(0,u.jsx)(f.span,{...e,ref:t,style:{...N,...e.style}}));R.displayName="VisuallyHidden";var S="ToastProvider",[L,k,D]=function(e){let t=e+"CollectionProvider",[n,r]=c(t),[i,a]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=e=>{let{scope:t,children:n}=e,r=o.useRef(null),a=o.useRef(new Map).current;return(0,u.jsx)(i,{scope:t,itemMap:a,collectionRef:r,children:n})};s.displayName=t;let f=e+"CollectionSlot",m=(0,d.Z8)(f),v=o.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=a(f,n),i=(0,l.e)(t,o.collectionRef);return(0,u.jsx)(m,{ref:i,children:r})});v.displayName=f;let p=e+"CollectionItemSlot",w="data-radix-collection-item",y=(0,d.Z8)(p),h=o.forwardRef((e,t)=>{let{scope:n,children:r,...i}=e,s=o.useRef(null),c=(0,l.e)(t,s),d=a(p,n);return o.useEffect(()=>(d.itemMap.set(s,{ref:s,...i}),()=>void d.itemMap.delete(s))),(0,u.jsx)(y,{[w]:"",ref:c,children:r})});return h.displayName=p,[{Provider:s,Slot:v,ItemSlot:h},function(t){let n=a(e+"CollectionConsumer",t);return o.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(w,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}("Toast"),[M,j]=c("Toast",[D]),[A,O]=M(S),I=e=>{let{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:i="right",swipeThreshold:a=50,children:s}=e,[l,c]=o.useState(null),[d,f]=o.useState(0),m=o.useRef(!1),v=o.useRef(!1);return n.trim()||console.error("Invalid prop `label` supplied to `".concat(S,"`. Expected non-empty `string`.")),(0,u.jsx)(L.Provider,{scope:t,children:(0,u.jsx)(A,{scope:t,label:n,duration:r,swipeDirection:i,swipeThreshold:a,toastCount:d,viewport:l,onViewportChange:c,onToastAdd:o.useCallback(()=>f(e=>e+1),[]),onToastRemove:o.useCallback(()=>f(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:m,isClosePausedRef:v,children:s})})};I.displayName=S;var _="ToastViewport",F=["F8"],K="toast.viewportPause",W="toast.viewportResume",U=o.forwardRef((e,t)=>{let{__scopeToast:n,hotkey:r=F,label:i="Notifications ({hotkey})",...a}=e,s=O(_,n),c=k(n),d=o.useRef(null),m=o.useRef(null),v=o.useRef(null),p=o.useRef(null),w=(0,l.e)(t,p,s.onViewportChange),y=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),E=s.toastCount>0;o.useEffect(()=>{let e=e=>{var t;0!==r.length&&r.every(t=>e[t]||e.code===t)&&(null===(t=p.current)||void 0===t||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[r]),o.useEffect(()=>{let e=d.current,t=p.current;if(E&&e&&t){let n=()=>{if(!s.isClosePausedRef.current){let e=new CustomEvent(K);t.dispatchEvent(e),s.isClosePausedRef.current=!0}},r=()=>{if(s.isClosePausedRef.current){let e=new CustomEvent(W);t.dispatchEvent(e),s.isClosePausedRef.current=!1}},o=t=>{e.contains(t.relatedTarget)||r()},i=()=>{e.contains(document.activeElement)||r()};return e.addEventListener("focusin",n),e.addEventListener("focusout",o),e.addEventListener("pointermove",n),e.addEventListener("pointerleave",i),window.addEventListener("blur",n),window.addEventListener("focus",r),()=>{e.removeEventListener("focusin",n),e.removeEventListener("focusout",o),e.removeEventListener("pointermove",n),e.removeEventListener("pointerleave",i),window.removeEventListener("blur",n),window.removeEventListener("focus",r)}}},[E,s.isClosePausedRef]);let b=o.useCallback(e=>{let{tabbingDirection:t}=e,n=c().map(e=>{let n=e.ref.current,r=[n,...function(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}(n)];return"forwards"===t?r:r.reverse()});return("forwards"===t?n.reverse():n).flat()},[c]);return o.useEffect(()=>{let e=p.current;if(e){let t=t=>{let n=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!n){var r,o,i;let n=document.activeElement,a=t.shiftKey;if(t.target===e&&a){null===(r=m.current)||void 0===r||r.focus();return}let s=b({tabbingDirection:a?"backwards":"forwards"}),l=s.findIndex(e=>e===n);ei(s.slice(l+1))?t.preventDefault():a?null===(o=m.current)||void 0===o||o.focus():null===(i=v.current)||void 0===i||i.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[c,b]),(0,u.jsxs)(h,{ref:d,role:"region","aria-label":i.replace("{hotkey}",y),tabIndex:-1,style:{pointerEvents:E?void 0:"none"},children:[E&&(0,u.jsx)(V,{ref:m,onFocusFromOutsideViewport:()=>{ei(b({tabbingDirection:"forwards"}))}}),(0,u.jsx)(L.Slot,{scope:n,children:(0,u.jsx)(f.ol,{tabIndex:-1,...a,ref:w})}),E&&(0,u.jsx)(V,{ref:v,onFocusFromOutsideViewport:()=>{ei(b({tabbingDirection:"backwards"}))}})]})});U.displayName=_;var $="ToastFocusProxy",V=o.forwardRef((e,t)=>{let{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,i=O($,n);return(0,u.jsx)(R,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:e=>{var t;let n=e.relatedTarget;(null===(t=i.viewport)||void 0===t?void 0:t.contains(n))||r()}})});V.displayName=$;var z="Toast",B=o.forwardRef((e,t)=>{let{forceMount:n,open:r,defaultOpen:i,onOpenChange:a,...l}=e,[c,d]=function({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,a,s]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),a=o.useRef(t);return P(()=>{a.current=t},[t]),o.useEffect(()=>{i.current!==n&&(a.current?.(n),i.current=n)},[n,i]),[n,r,a]}({defaultProp:t,onChange:n}),l=void 0!==e,u=l?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==l){let t=l?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=l},[l,r])}return[u,o.useCallback(t=>{if(l){let n="function"==typeof t?t(e):t;n!==e&&s.current?.(n)}else a(t)},[l,e,a,s])]}({prop:r,defaultProp:null==i||i,onChange:a,caller:z});return(0,u.jsx)(x,{present:n||c,children:(0,u.jsx)(Z,{open:c,...l,ref:t,onClose:()=>d(!1),onPause:v(e.onPause),onResume:v(e.onResume),onSwipeStart:s(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:s(e.onSwipeMove,e=>{let{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y","".concat(n,"px"))}),onSwipeCancel:s(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:s(e.onSwipeEnd,e=>{let{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y","".concat(n,"px")),d(!1)})})})});B.displayName=z;var[H,X]=M(z,{onClose(){}}),Z=o.forwardRef((e,t)=>{let{__scopeToast:n,type:r="foreground",duration:i,open:c,onClose:d,onEscapeKeyDown:m,onPause:p,onResume:w,onSwipeStart:h,onSwipeMove:E,onSwipeCancel:b,onSwipeEnd:g,...T}=e,x=O(z,n),[C,P]=o.useState(null),N=(0,l.e)(t,e=>P(e)),R=o.useRef(null),S=o.useRef(null),k=i||x.duration,D=o.useRef(0),M=o.useRef(k),j=o.useRef(0),{onToastAdd:A,onToastRemove:I}=x,_=v(()=>{var e;(null==C?void 0:C.contains(document.activeElement))&&(null===(e=x.viewport)||void 0===e||e.focus()),d()}),F=o.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(j.current),D.current=new Date().getTime(),j.current=window.setTimeout(_,e))},[_]);o.useEffect(()=>{let e=x.viewport;if(e){let t=()=>{F(M.current),null==w||w()},n=()=>{let e=new Date().getTime()-D.current;M.current=M.current-e,window.clearTimeout(j.current),null==p||p()};return e.addEventListener(K,n),e.addEventListener(W,t),()=>{e.removeEventListener(K,n),e.removeEventListener(W,t)}}},[x.viewport,k,p,w,F]),o.useEffect(()=>{c&&!x.isClosePausedRef.current&&F(k)},[c,k,x.isClosePausedRef,F]),o.useEffect(()=>(A(),()=>I()),[A,I]);let U=o.useMemo(()=>C?function e(t){let n=[];return Array.from(t.childNodes).forEach(t=>{if(t.nodeType===t.TEXT_NODE&&t.textContent&&n.push(t.textContent),t.nodeType===t.ELEMENT_NODE){let r=t.ariaHidden||t.hidden||"none"===t.style.display,o=""===t.dataset.radixToastAnnounceExclude;if(!r){if(o){let e=t.dataset.radixToastAnnounceAlt;e&&n.push(e)}else n.push(...e(t))}}}),n}(C):null,[C]);return x.viewport?(0,u.jsxs)(u.Fragment,{children:[U&&(0,u.jsx)(q,{__scopeToast:n,role:"status","aria-live":"foreground"===r?"assertive":"polite","aria-atomic":!0,children:U}),(0,u.jsx)(H,{scope:n,onClose:_,children:a.createPortal((0,u.jsx)(L.ItemSlot,{scope:n,children:(0,u.jsx)(y,{asChild:!0,onEscapeKeyDown:s(m,()=>{x.isFocusedToastEscapeKeyDownRef.current||_(),x.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,u.jsx)(f.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":c?"open":"closed","data-swipe-direction":x.swipeDirection,...T,ref:N,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:s(e.onKeyDown,e=>{"Escape"!==e.key||(null==m||m(e.nativeEvent),e.nativeEvent.defaultPrevented||(x.isFocusedToastEscapeKeyDownRef.current=!0,_()))}),onPointerDown:s(e.onPointerDown,e=>{0===e.button&&(R.current={x:e.clientX,y:e.clientY})}),onPointerMove:s(e.onPointerMove,e=>{if(!R.current)return;let t=e.clientX-R.current.x,n=e.clientY-R.current.y,r=!!S.current,o=["left","right"].includes(x.swipeDirection),i=["left","up"].includes(x.swipeDirection)?Math.min:Math.max,a=o?i(0,t):0,s=o?0:i(0,n),l="touch"===e.pointerType?10:2,u={x:a,y:s},c={originalEvent:e,delta:u};r?(S.current=u,er("toast.swipeMove",E,c,{discrete:!1})):eo(u,x.swipeDirection,l)?(S.current=u,er("toast.swipeStart",h,c,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>l||Math.abs(n)>l)&&(R.current=null)}),onPointerUp:s(e.onPointerUp,e=>{let t=S.current,n=e.target;if(n.hasPointerCapture(e.pointerId)&&n.releasePointerCapture(e.pointerId),S.current=null,R.current=null,t){let n=e.currentTarget,r={originalEvent:e,delta:t};eo(t,x.swipeDirection,x.swipeThreshold)?er("toast.swipeEnd",g,r,{discrete:!0}):er("toast.swipeCancel",b,r,{discrete:!0}),n.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),x.viewport)})]}):null}),q=e=>{let{__scopeToast:t,children:n,...r}=e,i=O(z,t),[a,s]=o.useState(!1),[l,c]=o.useState(!1);return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{},t=v(e);g(()=>{let e=0,n=0;return e=window.requestAnimationFrame(()=>n=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(n)}},[t])}(()=>s(!0)),o.useEffect(()=>{let e=window.setTimeout(()=>c(!0),1e3);return()=>window.clearTimeout(e)},[]),l?null:(0,u.jsx)(T,{asChild:!0,children:(0,u.jsx)(R,{...r,children:a&&(0,u.jsxs)(u.Fragment,{children:[i.label," ",n]})})})},Y=o.forwardRef((e,t)=>{let{__scopeToast:n,...r}=e;return(0,u.jsx)(f.div,{...r,ref:t})});Y.displayName="ToastTitle";var J=o.forwardRef((e,t)=>{let{__scopeToast:n,...r}=e;return(0,u.jsx)(f.div,{...r,ref:t})});J.displayName="ToastDescription";var G="ToastAction",Q=o.forwardRef((e,t)=>{let{altText:n,...r}=e;return n.trim()?(0,u.jsx)(en,{altText:n,asChild:!0,children:(0,u.jsx)(et,{...r,ref:t})}):(console.error("Invalid prop `altText` supplied to `".concat(G,"`. Expected non-empty `string`.")),null)});Q.displayName=G;var ee="ToastClose",et=o.forwardRef((e,t)=>{let{__scopeToast:n,...r}=e,o=X(ee,n);return(0,u.jsx)(en,{asChild:!0,children:(0,u.jsx)(f.button,{type:"button",...r,ref:t,onClick:s(e.onClick,o.onClose)})})});et.displayName=ee;var en=o.forwardRef((e,t)=>{let{__scopeToast:n,altText:r,...o}=e;return(0,u.jsx)(f.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})});function er(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.currentTarget,a=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?m(i,a):i.dispatchEvent(a)}var eo=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=Math.abs(e.x),o=Math.abs(e.y),i=r>o;return"left"===t||"right"===t?i&&r>n:!i&&o>n};function ei(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var ea=I,es=U,el=B,eu=Y,ec=J,ed=Q,ef=et},5922:function(e,t,n){"use strict";n.d(t,{f:function(){return u}});var r=n(2265),o=(e,t,n,r,o,i,a,s)=>{let l=document.documentElement,u=["light","dark"];function c(t){(Array.isArray(e)?e:[e]).forEach(e=>{let n="class"===e,r=n&&i?o.map(e=>i[e]||e):o;n?(l.classList.remove(...r),l.classList.add(i&&i[t]?i[t]:t)):l.setAttribute(e,t)}),s&&u.includes(t)&&(l.style.colorScheme=t)}if(r)c(r);else try{let e=localStorage.getItem(t)||n,r=a&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;c(r)}catch(e){}},i=["light","dark"],a="(prefers-color-scheme: dark)",s="undefined"==typeof window,l=r.createContext(void 0),u=e=>r.useContext(l)?r.createElement(r.Fragment,null,e.children):r.createElement(d,{...e}),c=["light","dark"],d=e=>{let{forcedTheme:t,disableTransitionOnChange:n=!1,enableSystem:o=!0,enableColorScheme:s=!0,storageKey:u="theme",themes:d=c,defaultTheme:w=o?"system":"light",attribute:y="data-theme",value:h,children:E,nonce:b,scriptProps:g}=e,[T,x]=r.useState(()=>m(u,w)),[C,P]=r.useState(()=>"system"===T?p():T),N=h?Object.values(h):d,R=r.useCallback(e=>{let t=e;if(!t)return;"system"===e&&o&&(t=p());let r=h?h[t]:t,a=n?v(b):null,l=document.documentElement,u=e=>{"class"===e?(l.classList.remove(...N),r&&l.classList.add(r)):e.startsWith("data-")&&(r?l.setAttribute(e,r):l.removeAttribute(e))};if(Array.isArray(y)?y.forEach(u):u(y),s){let e=i.includes(w)?w:null,n=i.includes(t)?t:e;l.style.colorScheme=n}null==a||a()},[b]),S=r.useCallback(e=>{let t="function"==typeof e?e(T):e;x(t);try{localStorage.setItem(u,t)}catch(e){}},[T]),L=r.useCallback(e=>{P(p(e)),"system"===T&&o&&!t&&R("system")},[T,t]);r.useEffect(()=>{let e=window.matchMedia(a);return e.addListener(L),L(e),()=>e.removeListener(L)},[L]),r.useEffect(()=>{let e=e=>{e.key===u&&(e.newValue?x(e.newValue):S(w))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[S]),r.useEffect(()=>{R(null!=t?t:T)},[t,T]);let k=r.useMemo(()=>({theme:T,setTheme:S,forcedTheme:t,resolvedTheme:"system"===T?C:T,themes:o?[...d,"system"]:d,systemTheme:o?C:void 0}),[T,S,t,C,o,d]);return r.createElement(l.Provider,{value:k},r.createElement(f,{forcedTheme:t,storageKey:u,attribute:y,enableSystem:o,enableColorScheme:s,defaultTheme:w,value:h,themes:d,nonce:b,scriptProps:g}),E)},f=r.memo(e=>{let{forcedTheme:t,storageKey:n,attribute:i,enableSystem:a,enableColorScheme:s,defaultTheme:l,value:u,themes:c,nonce:d,scriptProps:f}=e,m=JSON.stringify([i,n,l,t,c,u,a,s]).slice(1,-1);return r.createElement("script",{...f,suppressHydrationWarning:!0,nonce:"undefined"==typeof window?d:"",dangerouslySetInnerHTML:{__html:"(".concat(o.toString(),")(").concat(m,")")}})}),m=(e,t)=>{let n;if(!s){try{n=localStorage.getItem(e)||void 0}catch(e){}return n||t}},v=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},p=e=>(e||(e=window.matchMedia(a)),e.matches?"dark":"light")}}]);