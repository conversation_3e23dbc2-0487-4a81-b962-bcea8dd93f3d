import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/config/database'
import { Customer } from '@/lib/types'
import { v4 as uuidv4 } from 'uuid'

// GET /api/customers - Get all customers with pagination
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || ''

    const offset = (page - 1) * limit

    // Build WHERE clause
    let whereClause = 'WHERE 1=1'
    const params: any[] = []

    if (search) {
      whereClause += ' AND (name LIKE ? OR phone LIKE ? OR email LIKE ? OR customer_code LIKE ?)'
      const searchTerm = `%${search}%`
      params.push(searchTerm, searchTerm, searchTerm, searchTerm)
    }

    if (status) {
      whereClause += ' AND status = ?'
      params.push(status)
    }

    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM customers ${whereClause}`
    const countResult = await executeQuery<any[]>(countQuery, params)
    const total = countResult[0]?.total || 0

    // Get customers - use direct values for LIMIT/OFFSET to avoid MySQL parameter issues
    const query = `SELECT * FROM customers ${whereClause} ORDER BY created_at DESC LIMIT ${limit} OFFSET ${offset}`
    const customers = await executeQuery<Customer[]>(query, params)

    return NextResponse.json({
      success: true,
      data: {
        customers,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    })

  } catch (error) {
    console.error('Get customers error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch customers' },
      { status: 500 }
    )
  }
}

// POST /api/customers - Create new customer
export async function POST(request: NextRequest) {
  try {
    const data = await request.json()

    // Validate required fields
    if (!data.name) {
      return NextResponse.json(
        { success: false, error: 'Customer name is required' },
        { status: 400 }
      )
    }

    // Generate customer code
    const customerCode = await generateCustomerCode()

    const customerId = uuidv4()
    const now = new Date().toISOString()

    const customer = {
      id: customerId,
      customer_code: customerCode,
      name: data.name,
      phone: data.phone || null,
      alternate_phone: data.alternate_phone || null,
      email: data.email || null,
      date_of_birth: data.date_of_birth || null,
      anniversary_date: data.anniversary_date || null,
      gender: data.gender || null,
      address: data.address || null,
      city: data.city || null,
      state: data.state || null,
      pincode: data.pincode || null,
      country: data.country || 'India',
      aadhar_number: data.aadhar_number || null,
      pan_number: data.pan_number || null,
      passport_number: data.passport_number || null,
      driving_license: data.driving_license || null,
      kyc_status: 'pending',
      gst_number: data.gst_number || null,
      business_name: data.business_name || null,
      credit_limit: data.credit_limit || 0.00,
      customer_type: data.customer_type || 'individual',
      status: 'active',
      notes: data.notes || null,
      created_at: now,
      updated_at: now
    }

    await executeQuery(`
      INSERT INTO customers (
        id, customer_code, name, phone, alternate_phone, email, date_of_birth,
        anniversary_date, gender, address, city, state, pincode, country,
        aadhar_number, pan_number, passport_number, driving_license, kyc_status,
        gst_number, business_name, credit_limit, total_purchases, total_outstanding,
        loyalty_points, customer_type, status, notes, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      customer.id, customer.customer_code, customer.name, customer.phone,
      customer.alternate_phone, customer.email, customer.date_of_birth,
      customer.anniversary_date, customer.gender, customer.address,
      customer.city, customer.state, customer.pincode, customer.country,
      customer.aadhar_number, customer.pan_number, customer.passport_number,
      customer.driving_license, customer.kyc_status, customer.gst_number,
      customer.business_name, customer.credit_limit, 0.00, 0.00, 0,
      customer.customer_type, customer.status, customer.notes,
      customer.created_at, customer.updated_at
    ])

    return NextResponse.json({
      success: true,
      data: { customer },
      message: 'Customer created successfully'
    })

  } catch (error) {
    console.error('Create customer error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create customer' },
      { status: 500 }
    )
  }
}

// Generate customer code
async function generateCustomerCode(): Promise<string> {
  const year = new Date().getFullYear()
  
  // Get the count of customers created this year
  const countResult = await executeQuery<any[]>(
    'SELECT COUNT(*) as count FROM customers WHERE YEAR(created_at) = ?',
    [year]
  )
  
  const count = countResult[0]?.count || 0
  const nextNumber = (count + 1).toString().padStart(6, '0')
  
  return `CUST-${year}-${nextNumber}`
}
