import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/config/database'

// GET - Fetch dashboard summary data
export async function GET(request: NextRequest) {
  try {
    const today = new Date().toISOString().split('T')[0]
    const thisMonth = new Date().toISOString().slice(0, 7) // YYYY-MM format

    // Get today's sales
    const todaySalesQuery = `
      SELECT 
        COUNT(*) as count,
        COALESCE(SUM(total_amount), 0) as total
      FROM sales 
      WHERE DATE(created_at) = ? AND status = 'active'
    `
    const todaySales = await executeQuery<any[]>(todaySalesQuery, [today])

    // Get this month's sales
    const monthSalesQuery = `
      SELECT 
        COUNT(*) as count,
        COALESCE(SUM(total_amount), 0) as total
      FROM sales 
      WHERE DATE_FORMAT(created_at, '%Y-%m') = ? AND status = 'active'
    `
    const monthSales = await executeQuery<any[]>(monthSalesQuery, [thisMonth])

    // Get inventory summary
    const inventoryQuery = `
      SELECT 
        COUNT(*) as total_items,
        COALESCE(SUM(stock_quantity), 0) as total_stock,
        COALESCE(SUM(total_amount), 0) as total_value
      FROM inventory 
      WHERE status = 'available'
    `
    const inventory = await executeQuery<any[]>(inventoryQuery, [])

    // Get low stock items
    const lowStockQuery = `
      SELECT COUNT(*) as count
      FROM inventory 
      WHERE stock_quantity <= min_stock_level AND status = 'available'
    `
    const lowStock = await executeQuery<any[]>(lowStockQuery, [])

    // Get pending estimates
    const pendingEstimatesQuery = `
      SELECT COUNT(*) as count
      FROM estimates 
      WHERE status = 'pending' AND valid_till >= CURDATE()
    `
    let pendingEstimates = [{ count: 0 }]
    try {
      pendingEstimates = await executeQuery<any[]>(pendingEstimatesQuery, [])
    } catch (error) {
      // Table might not exist yet
      console.log('Estimates table not found, using default value')
    }

    // Get active repairs
    const activeRepairsQuery = `
      SELECT COUNT(*) as count
      FROM repairs 
      WHERE status IN ('received', 'in_progress')
    `
    let activeRepairs = [{ count: 0 }]
    try {
      activeRepairs = await executeQuery<any[]>(activeRepairsQuery, [])
    } catch (error) {
      // Table might not exist yet
      console.log('Repairs table not found, using default value')
    }

    // Get active schemes
    const activeSchemesQuery = `
      SELECT 
        COUNT(*) as count,
        COALESCE(SUM(total_amount), 0) as total_value
      FROM schemes 
      WHERE status = 'active'
    `
    let activeSchemes = [{ count: 0, total_value: 0 }]
    try {
      activeSchemes = await executeQuery<any[]>(activeSchemesQuery, [])
    } catch (error) {
      // Table might not exist yet
      console.log('Schemes table not found, using default value')
    }

    // Get recent sales for chart data
    const recentSalesQuery = `
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as count,
        COALESCE(SUM(total_amount), 0) as total
      FROM sales 
      WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) 
        AND status = 'active'
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `
    const recentSales = await executeQuery<any[]>(recentSalesQuery, [])

    const summary = {
      today: {
        sales_count: todaySales[0].count,
        sales_amount: todaySales[0].total
      },
      month: {
        sales_count: monthSales[0].count,
        sales_amount: monthSales[0].total
      },
      inventory: {
        total_items: inventory[0].total_items,
        total_stock: inventory[0].total_stock,
        total_value: inventory[0].total_value,
        low_stock_count: lowStock[0].count
      },
      pending: {
        estimates: pendingEstimates[0].count,
        repairs: activeRepairs[0].count
      },
      schemes: {
        active_count: activeSchemes[0].count,
        total_value: activeSchemes[0].total_value
      },
      recent_sales: recentSales
    }

    return NextResponse.json({
      success: true,
      data: summary
    })

  } catch (error) {
    console.error('Get dashboard summary error:', error)
    
    // Return default data on error
    const defaultSummary = {
      today: {
        sales_count: 0,
        sales_amount: 0
      },
      month: {
        sales_count: 0,
        sales_amount: 0
      },
      inventory: {
        total_items: 0,
        total_stock: 0,
        total_value: 0,
        low_stock_count: 0
      },
      pending: {
        estimates: 0,
        repairs: 0
      },
      schemes: {
        active_count: 0,
        total_value: 0
      },
      recent_sales: []
    }

    return NextResponse.json({
      success: true,
      data: defaultSummary,
      message: 'Using default data - some tables may not exist yet'
    })
  }
}
