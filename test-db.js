const mysql = require('mysql2/promise')

async function testDatabase() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'root',
      database: 'jewellery_pro_db'
    })

    console.log('✅ Database connected successfully')

    // Test if users table exists and has data
    const [users] = await connection.execute('SELECT COUNT(*) as count FROM users')
    console.log(`📊 Users table has ${users[0].count} records`)

    // Test specific user
    const [adminUser] = await connection.execute(
      'SELECT id, email, first_name, last_name, role FROM users WHERE email = ?',
      ['<EMAIL>']
    )
    
    if (adminUser.length > 0) {
      console.log('✅ Admin user found:', adminUser[0])
    } else {
      console.log('❌ Admin user not found')
    }

    await connection.end()
  } catch (error) {
    console.error('❌ Database error:', error.message)
  }
}

testDatabase()
