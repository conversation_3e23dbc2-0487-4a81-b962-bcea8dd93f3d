#!/usr/bin/env tsx

/**
 * Database Drop Script
 * Completely drops and recreates the database
 */

import { config } from 'dotenv'
import mysql from 'mysql2/promise'

// Load environment variables
config()

async function dropAndCreateDatabase() {
  console.log('🗑️ Dropping and recreating database...')
  console.log('=====================================')
  
  // Create connection without specifying database
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
  })
  
  try {
    // Drop the database if it exists
    console.log('📋 Dropping existing database...')
    await connection.query('DROP DATABASE IF EXISTS jewellery_pro_db')
    
    // Create the database
    console.log('🏗️ Creating new database...')
    await connection.query('CREATE DATABASE jewellery_pro_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci')
    
    console.log('✅ Database dropped and recreated successfully!')
    console.log('=====================================')
    console.log('You can now run: npx tsx scripts/database/init-database.ts')
    
  } catch (error) {
    console.error('❌ Database operation failed:', error)
    process.exit(1)
  } finally {
    await connection.end()
  }
}

// Run the operation
dropAndCreateDatabase()
