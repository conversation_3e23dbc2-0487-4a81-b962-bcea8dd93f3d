'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useToast } from '@/components/ui/simple-toast'
import {
  X,
  Save,
  Wrench,
  Calendar
} from 'lucide-react'

interface AddRepairDialogProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
}

interface RepairFormData {
  customer_id: string
  customer_name: string
  customer_phone: string
  item_description: string
  repair_type: string
  estimated_cost: number
  advance_paid: number
  received_date: string
  promised_date: string
  notes: string
}

const repairTypes = [
  'Ring Resizing',
  'Chain Repair',
  'Stone Setting',
  'Polishing',
  'Cleaning',
  'Clasp Repair',
  'Soldering',
  'Engraving',
  'Rhodium Plating',
  'Other'
]

export function AddRepairDialog({ isOpen, onClose, onSuccess }: AddRepairDialogProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [customers, setCustomers] = useState<any[]>([])
  const [selectedCustomer, setSelectedCustomer] = useState<any>(null)
  const { toast } = useToast()
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue
  } = useForm<RepairFormData>()

  useEffect(() => {
    if (isOpen) {
      loadCustomers()
      // Set default dates
      const today = new Date().toISOString().split('T')[0]
      const nextWeek = new Date()
      nextWeek.setDate(nextWeek.getDate() + 7)
      setValue('received_date', today)
      setValue('promised_date', nextWeek.toISOString().split('T')[0])
    }
  }, [isOpen, setValue])

  const loadCustomers = async () => {
    try {
      const response = await fetch('/api/customers')
      const result = await response.json()
      if (result.success) {
        setCustomers(result.data.customers)
      }
    } catch (error) {
      console.error('Failed to load customers:', error)
    }
  }

  const onSubmit = async (data: RepairFormData) => {
    setIsLoading(true)
    
    try {
      const response = await fetch('/api/repairs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: 'Repair Job Created',
          description: `Repair job ${result.data.repair_number} has been created`,
        })
        reset()
        setSelectedCustomer(null)
        onSuccess()
        onClose()
      } else {
        toast({
          title: 'Failed to Create Repair Job',
          description: result.error || 'An error occurred',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('Create repair error:', error)
      toast({
        title: 'Error',
        description: 'Failed to create repair job. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleCustomerSelect = (customerId: string) => {
    const customer = customers.find(c => c.id === customerId)
    if (customer) {
      setSelectedCustomer(customer)
      setValue('customer_id', customer.id)
      setValue('customer_name', customer.name)
      setValue('customer_phone', customer.phone)
    }
  }

  if (!isOpen) return null

  return (
    <>
      {/* Overlay */}
      <div className="fixed inset-0 bg-black/50 z-50" onClick={onClose} />
      
      {/* Dialog */}
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center space-x-2">
                  <Wrench className="w-5 h-5" />
                  <span>New Repair Job</span>
                </CardTitle>
                <CardDescription>Create a new jewelry repair job</CardDescription>
              </div>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="w-4 h-4" />
              </Button>
            </div>
          </CardHeader>
          
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Customer Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Customer Information</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Select Customer</label>
                    <select
                      onChange={(e) => handleCustomerSelect(e.target.value)}
                      className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    >
                      <option value="">Select existing customer</option>
                      {customers.map(customer => (
                        <option key={customer.id} value={customer.id}>
                          {customer.name} - {customer.phone}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Or Enter New Customer *</label>
                    <input
                      {...register('customer_name', { required: 'Customer name is required' })}
                      className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                      placeholder="Customer name"
                      value={selectedCustomer?.name || ''}
                      onChange={(e) => {
                        setValue('customer_name', e.target.value)
                        if (e.target.value !== selectedCustomer?.name) {
                          setSelectedCustomer(null)
                          setValue('customer_id', '')
                        }
                      }}
                    />
                    {errors.customer_name && (
                      <p className="text-red-500 text-xs mt-1">{errors.customer_name.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="text-sm font-medium">Phone Number</label>
                    <input
                      {...register('customer_phone')}
                      className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                      placeholder="Customer phone"
                      value={selectedCustomer?.phone || ''}
                      onChange={(e) => setValue('customer_phone', e.target.value)}
                    />
                  </div>
                </div>
              </div>

              {/* Repair Details */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Repair Details</h3>
                
                <div>
                  <label className="text-sm font-medium">Item Description *</label>
                  <textarea
                    {...register('item_description', { required: 'Item description is required' })}
                    rows={3}
                    className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="Describe the jewelry item in detail (e.g., Gold ring with diamond, Chain with broken clasp, etc.)"
                  />
                  {errors.item_description && (
                    <p className="text-red-500 text-xs mt-1">{errors.item_description.message}</p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Repair Type *</label>
                    <select
                      {...register('repair_type', { required: 'Repair type is required' })}
                      className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    >
                      <option value="">Select repair type</option>
                      {repairTypes.map(type => (
                        <option key={type} value={type}>{type}</option>
                      ))}
                    </select>
                    {errors.repair_type && (
                      <p className="text-red-500 text-xs mt-1">{errors.repair_type.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="text-sm font-medium">Estimated Cost (₹)</label>
                    <input
                      type="number"
                      step="10"
                      {...register('estimated_cost', { valueAsNumber: true })}
                      className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                      placeholder="1000"
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium">Advance Paid (₹)</label>
                    <input
                      type="number"
                      step="10"
                      {...register('advance_paid', { valueAsNumber: true })}
                      className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                      placeholder="500"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Received Date *</label>
                    <input
                      type="date"
                      {...register('received_date', { required: 'Received date is required' })}
                      className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    />
                    {errors.received_date && (
                      <p className="text-red-500 text-xs mt-1">{errors.received_date.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="text-sm font-medium">Promised Date</label>
                    <input
                      type="date"
                      {...register('promised_date')}
                      className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                      min={watch('received_date')}
                    />
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium">Notes</label>
                  <textarea
                    {...register('notes')}
                    rows={3}
                    className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="Additional notes about the repair..."
                  />
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end space-x-2 pt-4 border-t">
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      Create Repair Job
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </>
  )
}
