import { executeQuery } from '../../lib/config/database'
import { v4 as uuidv4 } from 'uuid'

async function seedSampleData() {
  try {
    console.log('🌱 Seeding sample data...')

    // Add sample customers
    const customers = [
      {
        id: uuidv4(),
        customer_code: 'CUST-PRI2024',
        name: '<PERSON><PERSON>',
        email: '<EMAIL>',
        phone: '+91 98765 43210',
        address: '123 MG Road',
        city: 'Mumbai',
        state: 'Maharashtra',
        pincode: '400001'
      },
      {
        id: uuidv4(),
        customer_code: 'CUST-RAJ2024',
        name: '<PERSON><PERSON>',
        email: 'raj<PERSON>.<EMAIL>',
        phone: '+91 87654 32109',
        address: '456 CP Street',
        city: 'Delhi',
        state: 'Delhi',
        pincode: '110001'
      },
      {
        id: uuidv4(),
        customer_code: 'CUST-ANI2024',
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '+91 76543 21098',
        address: '789 Ring Road',
        city: 'Ahmedabad',
        state: 'Gujarat',
        pincode: '380001'
      }
    ]

    for (const customer of customers) {
      await executeQuery(`
        INSERT IGNORE INTO customers (
          id, customer_code, name, email, phone, address, city, state, pincode,
          total_purchases, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        customer.id, customer.customer_code, customer.name, customer.email, customer.phone,
        customer.address, customer.city, customer.state, customer.pincode, 0, 'active'
      ])
    }

    console.log('✅ Sample customers added')

    // Add sample schemes
    const schemes = [
      {
        id: uuidv4(),
        scheme_code: 'GO2412-1001',
        customer_id: customers[0].id,
        customer_name: customers[0].name,
        customer_phone: customers[0].phone,
        scheme_type: 'gold',
        scheme_name: 'Gold Savings Plan - 12 Months',
        duration_months: 12,
        monthly_amount: 5000,
        total_amount: 60000,
        paid_amount: 25000,
        start_date: '2024-01-01',
        maturity_date: '2024-12-31',
        status: 'active'
      },
      {
        id: uuidv4(),
        scheme_code: 'SI2412-1002',
        customer_id: customers[1].id,
        customer_name: customers[1].name,
        customer_phone: customers[1].phone,
        scheme_type: 'silver',
        scheme_name: 'Silver Investment Plan - 24 Months',
        duration_months: 24,
        monthly_amount: 2000,
        total_amount: 48000,
        paid_amount: 16000,
        start_date: '2024-06-01',
        maturity_date: '2026-05-31',
        status: 'active'
      }
    ]

    for (const scheme of schemes) {
      await executeQuery(`
        INSERT IGNORE INTO schemes (
          id, scheme_code, customer_id, customer_name, customer_phone,
          scheme_type, scheme_name, duration_months, monthly_amount, total_amount,
          paid_amount, start_date, maturity_date, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        scheme.id, scheme.scheme_code, scheme.customer_id, scheme.customer_name, scheme.customer_phone,
        scheme.scheme_type, scheme.scheme_name, scheme.duration_months, scheme.monthly_amount,
        scheme.total_amount, scheme.paid_amount, scheme.start_date, scheme.maturity_date, scheme.status
      ])
    }

    console.log('✅ Sample schemes added')

    // Add sample repairs
    const repairs = [
      {
        id: uuidv4(),
        repair_number: 'REP-241201-1001',
        customer_id: customers[0].id,
        customer_name: customers[0].name,
        customer_phone: customers[0].phone,
        item_description: 'Gold chain with broken clasp, 22K gold, approximately 20 grams',
        repair_type: 'Clasp Repair',
        estimated_cost: 800,
        advance_paid: 400,
        status: 'in_progress',
        received_date: '2024-12-01',
        promised_date: '2024-12-08'
      },
      {
        id: uuidv4(),
        repair_number: 'REP-241202-1002',
        customer_id: customers[2].id,
        customer_name: customers[2].name,
        customer_phone: customers[2].phone,
        item_description: 'Diamond ring resizing from size 6 to size 7, white gold band',
        repair_type: 'Ring Resizing',
        estimated_cost: 1200,
        advance_paid: 600,
        status: 'completed',
        received_date: '2024-12-02',
        promised_date: '2024-12-09',
        completed_date: '2024-12-08'
      },
      {
        id: uuidv4(),
        repair_number: 'REP-241203-1003',
        customer_id: customers[1].id,
        customer_name: customers[1].name,
        customer_phone: customers[1].phone,
        item_description: 'Silver bracelet polishing and cleaning, antique design',
        repair_type: 'Polishing',
        estimated_cost: 300,
        advance_paid: 150,
        status: 'received',
        received_date: '2024-12-03',
        promised_date: '2024-12-06'
      }
    ]

    for (const repair of repairs) {
      await executeQuery(`
        INSERT IGNORE INTO repairs (
          id, repair_number, customer_id, customer_name, customer_phone,
          item_description, repair_type, estimated_cost, advance_paid,
          status, received_date, promised_date, completed_date, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        repair.id, repair.repair_number, repair.customer_id, repair.customer_name, repair.customer_phone,
        repair.item_description, repair.repair_type, repair.estimated_cost, repair.advance_paid,
        repair.status, repair.received_date, repair.promised_date, repair.completed_date || null
      ])
    }

    console.log('✅ Sample repairs added')

    // Add sample sales
    const sales = [
      {
        id: uuidv4(),
        invoice_number: 'INV-*************',
        customer_id: customers[0].id,
        customer_name: customers[0].name,
        customer_phone: customers[0].phone,
        subtotal: 125000,
        discount_amount: 5000,
        tax_amount: 0,
        total_amount: 120000,
        payment_method: 'card',
        payment_status: 'paid',
        status: 'active'
      },
      {
        id: uuidv4(),
        invoice_number: 'INV-*************',
        customer_id: customers[1].id,
        customer_name: customers[1].name,
        customer_phone: customers[1].phone,
        subtotal: 45000,
        discount_amount: 0,
        tax_amount: 0,
        total_amount: 45000,
        payment_method: 'cash',
        payment_status: 'paid',
        status: 'active'
      }
    ]

    for (const sale of sales) {
      await executeQuery(`
        INSERT IGNORE INTO sales (
          id, invoice_number, customer_id, customer_name, customer_phone,
          subtotal, discount_amount, tax_amount, total_amount,
          payment_method, payment_status, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        sale.id, sale.invoice_number, sale.customer_id, sale.customer_name, sale.customer_phone,
        sale.subtotal, sale.discount_amount, sale.tax_amount, sale.total_amount,
        sale.payment_method, sale.payment_status, sale.status
      ])
    }

    console.log('✅ Sample sales added')

    // Add scheme payments
    const schemePayments = [
      {
        id: uuidv4(),
        scheme_id: schemes[0].id,
        payment_date: '2024-01-01',
        amount: 5000,
        payment_method: 'upi',
        receipt_number: 'RCP-**********'
      },
      {
        id: uuidv4(),
        scheme_id: schemes[0].id,
        payment_date: '2024-02-01',
        amount: 5000,
        payment_method: 'upi',
        receipt_number: 'RCP-**********'
      },
      {
        id: uuidv4(),
        scheme_id: schemes[0].id,
        payment_date: '2024-03-01',
        amount: 5000,
        payment_method: 'upi',
        receipt_number: 'RCP-**********'
      },
      {
        id: uuidv4(),
        scheme_id: schemes[0].id,
        payment_date: '2024-04-01',
        amount: 5000,
        payment_method: 'upi',
        receipt_number: 'RCP-240401-001'
      },
      {
        id: uuidv4(),
        scheme_id: schemes[0].id,
        payment_date: '2024-05-01',
        amount: 5000,
        payment_method: 'upi',
        receipt_number: 'RCP-240501-001'
      }
    ]

    for (const payment of schemePayments) {
      await executeQuery(`
        INSERT IGNORE INTO scheme_payments (
          id, scheme_id, payment_date, amount, payment_method, receipt_number, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, NOW())
      `, [
        payment.id, payment.scheme_id, payment.payment_date, payment.amount,
        payment.payment_method, payment.receipt_number
      ])
    }

    console.log('✅ Sample scheme payments added')

    console.log('🎉 Sample data seeding completed successfully!')

  } catch (error) {
    console.error('❌ Error seeding sample data:', error)
    throw error
  }
}

// Run the seeding if this file is executed directly
if (require.main === module) {
  seedSampleData()
    .then(() => {
      console.log('✅ Seeding completed')
      process.exit(0)
    })
    .catch((error) => {
      console.error('❌ Seeding failed:', error)
      process.exit(1)
    })
}

export { seedSampleData }
