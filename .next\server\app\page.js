(()=>{var e={};e.id=931,e.ids=[931],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},746:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>f,originalPathname:()=>c,pages:()=>u,routeModule:()=>m,tree:()=>d}),r(908),r(9030),r(5866);var s=r(3191),a=r(8716),i=r(7922),l=r.n(i),n=r(5231),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(t,o);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,908)),"C:\\proj\\jewellery-pro\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,9030)),"C:\\proj\\jewellery-pro\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,5866,23)),"next/dist/client/components/not-found-error"]}],u=["C:\\proj\\jewellery-pro\\app\\page.tsx"],c="/page",f={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8884:(e,t,r)=>{Promise.resolve().then(r.bind(r,2506))},5871:(e,t,r)=>{Promise.resolve().then(r.bind(r,5813)),Promise.resolve().then(r.bind(r,7083))},1686:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2994,23)),Promise.resolve().then(r.t.bind(r,6114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,9671,23)),Promise.resolve().then(r.t.bind(r,1868,23)),Promise.resolve().then(r.t.bind(r,4759,23))},2506:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>eA});var s=r(326),a=r(7577),i=r(5047),l=e=>"checkbox"===e.type,n=e=>e instanceof Date,o=e=>null==e;let d=e=>"object"==typeof e;var u=e=>!o(e)&&!Array.isArray(e)&&d(e)&&!n(e),c=e=>u(e)&&e.target?l(e.target)?e.target.checked:e.target.value:e,f=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,m=(e,t)=>e.has(f(t)),p=e=>{let t=e.constructor&&e.constructor.prototype;return u(t)&&t.hasOwnProperty("isPrototypeOf")},h="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function y(e){let t;let r=Array.isArray(e),s="undefined"!=typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else if(!(!(h&&(e instanceof Blob||s))&&(r||u(e))))return e;else if(t=r?[]:{},r||p(e))for(let r in e)e.hasOwnProperty(r)&&(t[r]=y(e[r]));else t=e;return t}var x=e=>/^\w*$/.test(e),v=e=>void 0===e,g=e=>Array.isArray(e)?e.filter(Boolean):[],b=e=>g(e.replace(/["|']|\]/g,"").split(/\.|\[/)),w=(e,t,r)=>{if(!t||!u(e))return r;let s=(x(t)?[t]:b(t)).reduce((e,t)=>o(e)?e:e[t],e);return v(s)||s===e?v(e[t])?r:e[t]:s},j=e=>"boolean"==typeof e,_=(e,t,r)=>{let s=-1,a=x(t)?[t]:b(t),i=a.length,l=i-1;for(;++s<i;){let t=a[s],i=r;if(s!==l){let r=e[t];i=u(r)||Array.isArray(r)?r:isNaN(+a[s+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=i,e=e[t]}};let S={BLUR:"blur",FOCUS_OUT:"focusout"},k={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},N={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"};a.createContext(null).displayName="HookFormContext";var A=(e,t,r,s=!0)=>{let a={defaultValues:t._defaultValues};for(let i in e)Object.defineProperty(a,i,{get:()=>(t._proxyFormState[i]!==k.all&&(t._proxyFormState[i]=!s||k.all),r&&(r[i]=!0),e[i])});return a};let V="undefined"!=typeof window?a.useLayoutEffect:a.useEffect;var F=e=>"string"==typeof e,D=(e,t,r,s,a)=>F(e)?(s&&t.watch.add(e),w(r,e,a)):Array.isArray(e)?e.map(e=>(s&&t.watch.add(e),w(r,e))):(s&&(t.watchAll=!0),r),C=e=>o(e)||!d(e);function T(e,t,r=new WeakSet){if(C(e)||C(t))return e===t;if(n(e)&&n(t))return e.getTime()===t.getTime();let s=Object.keys(e),a=Object.keys(t);if(s.length!==a.length)return!1;if(r.has(e)||r.has(t))return!0;for(let i of(r.add(e),r.add(t),s)){let s=e[i];if(!a.includes(i))return!1;if("ref"!==i){let e=t[i];if(n(s)&&n(e)||u(s)&&u(e)||Array.isArray(s)&&Array.isArray(e)?!T(s,e,r):s!==e)return!1}}return!0}var E=(e,t,r,s,a)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[s]:a||!0}}:{},O=e=>Array.isArray(e)?e:[e],M=()=>{let e=[];return{get observers(){return e},next:t=>{for(let r of e)r.next&&r.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter(e=>e!==t)}}),unsubscribe:()=>{e=[]}}},P=e=>u(e)&&!Object.keys(e).length,L=e=>"file"===e.type,R=e=>"function"==typeof e,q=e=>{if(!h)return!1;let t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},B=e=>"select-multiple"===e.type,U=e=>"radio"===e.type,I=e=>U(e)||l(e),W=e=>q(e)&&e.isConnected;function z(e,t){let r=Array.isArray(t)?t:x(t)?[t]:b(t),s=1===r.length?e:function(e,t){let r=t.slice(0,-1).length,s=0;for(;s<r;)e=v(e)?s++:e[t[s++]];return e}(e,r),a=r.length-1,i=r[a];return s&&delete s[i],0!==a&&(u(s)&&P(s)||Array.isArray(s)&&function(e){for(let t in e)if(e.hasOwnProperty(t)&&!v(e[t]))return!1;return!0}(s))&&z(e,r.slice(0,-1)),e}var H=e=>{for(let t in e)if(R(e[t]))return!0;return!1};function G(e,t={}){let r=Array.isArray(e);if(u(e)||r)for(let r in e)Array.isArray(e[r])||u(e[r])&&!H(e[r])?(t[r]=Array.isArray(e[r])?[]:{},G(e[r],t[r])):o(e[r])||(t[r]=!0);return t}var J=(e,t)=>(function e(t,r,s){let a=Array.isArray(t);if(u(t)||a)for(let a in t)Array.isArray(t[a])||u(t[a])&&!H(t[a])?v(r)||C(s[a])?s[a]=Array.isArray(t[a])?G(t[a],[]):{...G(t[a])}:e(t[a],o(r)?{}:r[a],s[a]):s[a]=!T(t[a],r[a]);return s})(e,t,G(t));let $={value:!1,isValid:!1},Z={value:!0,isValid:!0};var X=e=>{if(Array.isArray(e)){if(e.length>1){let t=e.filter(e=>e&&e.checked&&!e.disabled).map(e=>e.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!v(e[0].attributes.value)?v(e[0].value)||""===e[0].value?Z:{value:e[0].value,isValid:!0}:Z:$}return $},K=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:s})=>v(e)?e:t?""===e?NaN:e?+e:e:r&&F(e)?new Date(e):s?s(e):e;let Q={isValid:!1,value:null};var Y=e=>Array.isArray(e)?e.reduce((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e,Q):Q;function ee(e){let t=e.ref;return L(t)?t.files:U(t)?Y(e.refs).value:B(t)?[...t.selectedOptions].map(({value:e})=>e):l(t)?X(e.refs).value:K(v(t.value)?e.ref.value:t.value,e)}var et=(e,t,r,s)=>{let a={};for(let r of e){let e=w(t,r);e&&_(a,r,e._f)}return{criteriaMode:r,names:[...e],fields:a,shouldUseNativeValidation:s}},er=e=>e instanceof RegExp,es=e=>v(e)?e:er(e)?e.source:u(e)?er(e.value)?e.value.source:e.value:e,ea=e=>({isOnSubmit:!e||e===k.onSubmit,isOnBlur:e===k.onBlur,isOnChange:e===k.onChange,isOnAll:e===k.all,isOnTouch:e===k.onTouched});let ei="AsyncFunction";var el=e=>!!e&&!!e.validate&&!!(R(e.validate)&&e.validate.constructor.name===ei||u(e.validate)&&Object.values(e.validate).find(e=>e.constructor.name===ei)),en=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),eo=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some(t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length))));let ed=(e,t,r,s)=>{for(let a of r||Object.keys(e)){let r=w(e,a);if(r){let{_f:e,...i}=r;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],a)&&!s||e.ref&&t(e.ref,e.name)&&!s)return!0;if(ed(i,t))break}else if(u(i)&&ed(i,t))break}}};function eu(e,t,r){let s=w(e,r);if(s||x(r))return{error:s,name:r};let a=r.split(".");for(;a.length;){let s=a.join("."),i=w(t,s),l=w(e,s);if(i&&!Array.isArray(i)&&r!==s)break;if(l&&l.type)return{name:s,error:l};if(l&&l.root&&l.root.type)return{name:`${s}.root`,error:l.root};a.pop()}return{name:r}}var ec=(e,t,r,s)=>{r(e);let{name:a,...i}=e;return P(i)||Object.keys(i).length>=Object.keys(t).length||Object.keys(i).find(e=>t[e]===(!s||k.all))},ef=(e,t,r)=>!e||!t||e===t||O(e).some(e=>e&&(r?e===t:e.startsWith(t)||t.startsWith(e))),em=(e,t,r,s,a)=>!a.isOnAll&&(!r&&a.isOnTouch?!(t||e):(r?s.isOnBlur:a.isOnBlur)?!e:(r?!s.isOnChange:!a.isOnChange)||e),ep=(e,t)=>!g(w(e,t)).length&&z(e,t),eh=(e,t,r)=>{let s=O(w(e,r));return _(s,"root",t[r]),_(e,r,s),e},ey=e=>F(e);function ex(e,t,r="validate"){if(ey(e)||Array.isArray(e)&&e.every(ey)||j(e)&&!e)return{type:r,message:ey(e)?e:"",ref:t}}var ev=e=>u(e)&&!er(e)?e:{value:e,message:""},eg=async(e,t,r,s,a,i)=>{let{ref:n,refs:d,required:c,maxLength:f,minLength:m,min:p,max:h,pattern:y,validate:x,name:g,valueAsNumber:b,mount:_}=e._f,S=w(r,g);if(!_||t.has(g))return{};let k=d?d[0]:n,A=e=>{a&&k.reportValidity&&(k.setCustomValidity(j(e)?"":e||""),k.reportValidity())},V={},D=U(n),C=l(n),T=(b||L(n))&&v(n.value)&&v(S)||q(n)&&""===n.value||""===S||Array.isArray(S)&&!S.length,O=E.bind(null,g,s,V),M=(e,t,r,s=N.maxLength,a=N.minLength)=>{let i=e?t:r;V[g]={type:e?s:a,message:i,ref:n,...O(e?s:a,i)}};if(i?!Array.isArray(S)||!S.length:c&&(!(D||C)&&(T||o(S))||j(S)&&!S||C&&!X(d).isValid||D&&!Y(d).isValid)){let{value:e,message:t}=ey(c)?{value:!!c,message:c}:ev(c);if(e&&(V[g]={type:N.required,message:t,ref:k,...O(N.required,t)},!s))return A(t),V}if(!T&&(!o(p)||!o(h))){let e,t;let r=ev(h),a=ev(p);if(o(S)||isNaN(S)){let s=n.valueAsDate||new Date(S),i=e=>new Date(new Date().toDateString()+" "+e),l="time"==n.type,o="week"==n.type;F(r.value)&&S&&(e=l?i(S)>i(r.value):o?S>r.value:s>new Date(r.value)),F(a.value)&&S&&(t=l?i(S)<i(a.value):o?S<a.value:s<new Date(a.value))}else{let s=n.valueAsNumber||(S?+S:S);o(r.value)||(e=s>r.value),o(a.value)||(t=s<a.value)}if((e||t)&&(M(!!e,r.message,a.message,N.max,N.min),!s))return A(V[g].message),V}if((f||m)&&!T&&(F(S)||i&&Array.isArray(S))){let e=ev(f),t=ev(m),r=!o(e.value)&&S.length>+e.value,a=!o(t.value)&&S.length<+t.value;if((r||a)&&(M(r,e.message,t.message),!s))return A(V[g].message),V}if(y&&!T&&F(S)){let{value:e,message:t}=ev(y);if(er(e)&&!S.match(e)&&(V[g]={type:N.pattern,message:t,ref:n,...O(N.pattern,t)},!s))return A(t),V}if(x){if(R(x)){let e=ex(await x(S,r),k);if(e&&(V[g]={...e,...O(N.validate,e.message)},!s))return A(e.message),V}else if(u(x)){let e={};for(let t in x){if(!P(e)&&!s)break;let a=ex(await x[t](S,r),k,t);a&&(e={...a,...O(t,a.message)},A(a.message),s&&(V[g]=e))}if(!P(e)&&(V[g]={ref:k,...e},!s))return V}}return A(!0),V};let eb={mode:k.onSubmit,reValidateMode:k.onChange,shouldFocusError:!0};var ew=r(2881);let ej=(0,ew.Z)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),e_=(0,ew.Z)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),eS=(0,ew.Z)("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]]);var ek=r(7068);function eN({onSuccess:e}){let[t,r]=(0,a.useState)(!1),[i,d]=(0,a.useState)(!1),{toast:f}=(0,ek.pm)(),{register:p,handleSubmit:x,formState:{errors:b}}=function(e={}){let t=a.useRef(void 0),r=a.useRef(void 0),[s,i]=a.useState({isDirty:!1,isValidating:!1,isLoading:R(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:R(e.defaultValues)?void 0:e.defaultValues});if(!t.current){if(e.formControl)t.current={...e.formControl,formState:s},e.defaultValues&&!R(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions);else{let{formControl:r,...a}=function(e={}){let t,r={...eb,...e},s={submitCount:0,isDirty:!1,isReady:!1,isLoading:R(r.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:r.errors||{},disabled:r.disabled||!1},a={},i=(u(r.defaultValues)||u(r.values))&&y(r.defaultValues||r.values)||{},d=r.shouldUnregister?{}:y(i),f={action:!1,mount:!1,watch:!1},p={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},x=0,b={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},N={...b},A={array:M(),state:M()},V=r.criteriaMode===k.all,C=e=>t=>{clearTimeout(x),x=setTimeout(e,t)},E=async e=>{if(!r.disabled&&(b.isValid||N.isValid||e)){let e=r.resolver?P((await X()).errors):await Y(a,!0);e!==s.isValid&&A.state.next({isValid:e})}},U=(e,t)=>{!r.disabled&&(b.isValidating||b.validatingFields||N.isValidating||N.validatingFields)&&((e||Array.from(p.mount)).forEach(e=>{e&&(t?_(s.validatingFields,e,t):z(s.validatingFields,e))}),A.state.next({validatingFields:s.validatingFields,isValidating:!P(s.validatingFields)}))},H=(e,t)=>{_(s.errors,e,t),A.state.next({errors:s.errors})},G=(e,t,r,s)=>{let l=w(a,e);if(l){let a=w(d,e,v(r)?w(i,e):r);v(a)||s&&s.defaultChecked||t?_(d,e,t?a:ee(l._f)):ey(e,a),f.mount&&E()}},$=(e,t,a,l,n)=>{let o=!1,d=!1,u={name:e};if(!r.disabled){if(!a||l){(b.isDirty||N.isDirty)&&(d=s.isDirty,s.isDirty=u.isDirty=er(),o=d!==u.isDirty);let r=T(w(i,e),t);d=!!w(s.dirtyFields,e),r?z(s.dirtyFields,e):_(s.dirtyFields,e,!0),u.dirtyFields=s.dirtyFields,o=o||(b.dirtyFields||N.dirtyFields)&&!r!==d}if(a){let t=w(s.touchedFields,e);t||(_(s.touchedFields,e,a),u.touchedFields=s.touchedFields,o=o||(b.touchedFields||N.touchedFields)&&t!==a)}o&&n&&A.state.next(u)}return o?u:{}},Z=(e,a,i,l)=>{let n=w(s.errors,e),o=(b.isValid||N.isValid)&&j(a)&&s.isValid!==a;if(r.delayError&&i?(t=C(()=>H(e,i)))(r.delayError):(clearTimeout(x),t=null,i?_(s.errors,e,i):z(s.errors,e)),(i?!T(n,i):n)||!P(l)||o){let t={...l,...o&&j(a)?{isValid:a}:{},errors:s.errors,name:e};s={...s,...t},A.state.next(t)}},X=async e=>{U(e,!0);let t=await r.resolver(d,r.context,et(e||p.mount,a,r.criteriaMode,r.shouldUseNativeValidation));return U(e),t},Q=async e=>{let{errors:t}=await X(e);if(e)for(let r of e){let e=w(t,r);e?_(s.errors,r,e):z(s.errors,r)}else s.errors=t;return t},Y=async(e,t,a={valid:!0})=>{for(let i in e){let l=e[i];if(l){let{_f:e,...n}=l;if(e){let n=p.array.has(e.name),o=l._f&&el(l._f);o&&b.validatingFields&&U([i],!0);let u=await eg(l,p.disabled,d,V,r.shouldUseNativeValidation&&!t,n);if(o&&b.validatingFields&&U([i]),u[e.name]&&(a.valid=!1,t))break;t||(w(u,e.name)?n?eh(s.errors,u,e.name):_(s.errors,e.name,u[e.name]):z(s.errors,e.name))}P(n)||await Y(n,t,a)}}return a.valid},er=(e,t)=>!r.disabled&&(e&&t&&_(d,e,t),!T(eS(),i)),ei=(e,t,r)=>D(e,p,{...f.mount?d:v(t)?i:F(e)?{[e]:t}:t},r,t),ey=(e,t,r={})=>{let s=w(a,e),i=t;if(s){let r=s._f;r&&(r.disabled||_(d,e,K(t,r)),i=q(r.ref)&&o(t)?"":t,B(r.ref)?[...r.ref.options].forEach(e=>e.selected=i.includes(e.value)):r.refs?l(r.ref)?r.refs.forEach(e=>{e.defaultChecked&&e.disabled||(Array.isArray(i)?e.checked=!!i.find(t=>t===e.value):e.checked=i===e.value||!!i)}):r.refs.forEach(e=>e.checked=e.value===i):L(r.ref)?r.ref.value="":(r.ref.value=i,r.ref.type||A.state.next({name:e,values:y(d)})))}(r.shouldDirty||r.shouldTouch)&&$(e,i,r.shouldTouch,r.shouldDirty,!0),r.shouldValidate&&e_(e)},ex=(e,t,r)=>{for(let s in t){if(!t.hasOwnProperty(s))return;let i=t[s],l=e+"."+s,o=w(a,l);(p.array.has(e)||u(i)||o&&!o._f)&&!n(i)?ex(l,i,r):ey(l,i,r)}},ev=(e,t,r={})=>{let l=w(a,e),n=p.array.has(e),u=y(t);_(d,e,u),n?(A.array.next({name:e,values:y(d)}),(b.isDirty||b.dirtyFields||N.isDirty||N.dirtyFields)&&r.shouldDirty&&A.state.next({name:e,dirtyFields:J(i,d),isDirty:er(e,u)})):!l||l._f||o(u)?ey(e,u,r):ex(e,u,r),eo(e,p)&&A.state.next({...s,name:e}),A.state.next({name:f.mount?e:void 0,values:y(d)})},ew=async e=>{f.mount=!0;let i=e.target,l=i.name,o=!0,u=w(a,l),m=e=>{o=Number.isNaN(e)||n(e)&&isNaN(e.getTime())||T(e,w(d,l,e))},h=ea(r.mode),x=ea(r.reValidateMode);if(u){let n,f;let v=i.type?ee(u._f):c(e),g=e.type===S.BLUR||e.type===S.FOCUS_OUT,j=!en(u._f)&&!r.resolver&&!w(s.errors,l)&&!u._f.deps||em(g,w(s.touchedFields,l),s.isSubmitted,x,h),k=eo(l,p,g);_(d,l,v),g?(u._f.onBlur&&u._f.onBlur(e),t&&t(0)):u._f.onChange&&u._f.onChange(e);let F=$(l,v,g),D=!P(F)||k;if(g||A.state.next({name:l,type:e.type,values:y(d)}),j)return(b.isValid||N.isValid)&&("onBlur"===r.mode?g&&E():g||E()),D&&A.state.next({name:l,...k?{}:F});if(!g&&k&&A.state.next({...s}),r.resolver){let{errors:e}=await X([l]);if(m(v),o){let t=eu(s.errors,a,l),r=eu(e,a,t.name||l);n=r.error,l=r.name,f=P(e)}}else U([l],!0),n=(await eg(u,p.disabled,d,V,r.shouldUseNativeValidation))[l],U([l]),m(v),o&&(n?f=!1:(b.isValid||N.isValid)&&(f=await Y(a,!0)));o&&(u._f.deps&&e_(u._f.deps),Z(l,f,n,F))}},ej=(e,t)=>{if(w(s.errors,t)&&e.focus)return e.focus(),1},e_=async(e,t={})=>{let i,l;let n=O(e);if(r.resolver){let t=await Q(v(e)?e:n);i=P(t),l=e?!n.some(e=>w(t,e)):i}else e?((l=(await Promise.all(n.map(async e=>{let t=w(a,e);return await Y(t&&t._f?{[e]:t}:t)}))).every(Boolean))||s.isValid)&&E():l=i=await Y(a);return A.state.next({...!F(e)||(b.isValid||N.isValid)&&i!==s.isValid?{}:{name:e},...r.resolver||!e?{isValid:i}:{},errors:s.errors}),t.shouldFocus&&!l&&ed(a,ej,e?n:p.mount),l},eS=e=>{let t={...f.mount?d:i};return v(e)?t:F(e)?w(t,e):e.map(e=>w(t,e))},ek=(e,t)=>({invalid:!!w((t||s).errors,e),isDirty:!!w((t||s).dirtyFields,e),error:w((t||s).errors,e),isValidating:!!w(s.validatingFields,e),isTouched:!!w((t||s).touchedFields,e)}),eN=(e,t,r)=>{let i=(w(a,e,{_f:{}})._f||{}).ref,{ref:l,message:n,type:o,...d}=w(s.errors,e)||{};_(s.errors,e,{...d,...t,ref:i}),A.state.next({name:e,errors:s.errors,isValid:!1}),r&&r.shouldFocus&&i&&i.focus&&i.focus()},eA=e=>A.state.subscribe({next:t=>{ef(e.name,t.name,e.exact)&&ec(t,e.formState||b,eM,e.reRenderRoot)&&e.callback({values:{...d},...s,...t,defaultValues:i})}}).unsubscribe,eV=(e,t={})=>{for(let l of e?O(e):p.mount)p.mount.delete(l),p.array.delete(l),t.keepValue||(z(a,l),z(d,l)),t.keepError||z(s.errors,l),t.keepDirty||z(s.dirtyFields,l),t.keepTouched||z(s.touchedFields,l),t.keepIsValidating||z(s.validatingFields,l),r.shouldUnregister||t.keepDefaultValue||z(i,l);A.state.next({values:y(d)}),A.state.next({...s,...t.keepDirty?{isDirty:er()}:{}}),t.keepIsValid||E()},eF=({disabled:e,name:t})=>{(j(e)&&f.mount||e||p.disabled.has(t))&&(e?p.disabled.add(t):p.disabled.delete(t))},eD=(e,t={})=>{let s=w(a,e),l=j(t.disabled)||j(r.disabled);return _(a,e,{...s||{},_f:{...s&&s._f?s._f:{ref:{name:e}},name:e,mount:!0,...t}}),p.mount.add(e),s?eF({disabled:j(t.disabled)?t.disabled:r.disabled,name:e}):G(e,!0,t.value),{...l?{disabled:t.disabled||r.disabled}:{},...r.progressive?{required:!!t.required,min:es(t.min),max:es(t.max),minLength:es(t.minLength),maxLength:es(t.maxLength),pattern:es(t.pattern)}:{},name:e,onChange:ew,onBlur:ew,ref:l=>{if(l){eD(e,t),s=w(a,e);let r=v(l.value)&&l.querySelectorAll&&l.querySelectorAll("input,select,textarea")[0]||l,n=I(r),o=s._f.refs||[];(n?o.find(e=>e===r):r===s._f.ref)||(_(a,e,{_f:{...s._f,...n?{refs:[...o.filter(W),r,...Array.isArray(w(i,e))?[{}]:[]],ref:{type:r.type,name:e}}:{ref:r}}}),G(e,!1,void 0,r))}else(s=w(a,e,{}))._f&&(s._f.mount=!1),(r.shouldUnregister||t.shouldUnregister)&&!(m(p.array,e)&&f.action)&&p.unMount.add(e)}}},eC=()=>r.shouldFocusError&&ed(a,ej,p.mount),eT=(e,t)=>async i=>{let l;i&&(i.preventDefault&&i.preventDefault(),i.persist&&i.persist());let n=y(d);if(A.state.next({isSubmitting:!0}),r.resolver){let{errors:e,values:t}=await X();s.errors=e,n=y(t)}else await Y(a);if(p.disabled.size)for(let e of p.disabled)z(n,e);if(z(s.errors,"root"),P(s.errors)){A.state.next({errors:{}});try{await e(n,i)}catch(e){l=e}}else t&&await t({...s.errors},i),eC(),setTimeout(eC);if(A.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:P(s.errors)&&!l,submitCount:s.submitCount+1,errors:s.errors}),l)throw l},eE=(e,t={})=>{let l=e?y(e):i,n=y(l),o=P(e),u=o?i:n;if(t.keepDefaultValues||(i=l),!t.keepValues){if(t.keepDirtyValues)for(let e of Array.from(new Set([...p.mount,...Object.keys(J(i,d))])))w(s.dirtyFields,e)?_(u,e,w(d,e)):ev(e,w(u,e));else{if(h&&v(e))for(let e of p.mount){let t=w(a,e);if(t&&t._f){let e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(q(e)){let t=e.closest("form");if(t){t.reset();break}}}}if(t.keepFieldsRef)for(let e of p.mount)ev(e,w(u,e));else a={}}d=r.shouldUnregister?t.keepDefaultValues?y(i):{}:y(u),A.array.next({values:{...u}}),A.state.next({values:{...u}})}p={mount:t.keepDirtyValues?p.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},f.mount=!b.isValid||!!t.keepIsValid||!!t.keepDirtyValues,f.watch=!!r.shouldUnregister,A.state.next({submitCount:t.keepSubmitCount?s.submitCount:0,isDirty:!o&&(t.keepDirty?s.isDirty:!!(t.keepDefaultValues&&!T(e,i))),isSubmitted:!!t.keepIsSubmitted&&s.isSubmitted,dirtyFields:o?{}:t.keepDirtyValues?t.keepDefaultValues&&d?J(i,d):s.dirtyFields:t.keepDefaultValues&&e?J(i,e):t.keepDirty?s.dirtyFields:{},touchedFields:t.keepTouched?s.touchedFields:{},errors:t.keepErrors?s.errors:{},isSubmitSuccessful:!!t.keepIsSubmitSuccessful&&s.isSubmitSuccessful,isSubmitting:!1})},eO=(e,t)=>eE(R(e)?e(d):e,t),eM=e=>{s={...s,...e}},eP={control:{register:eD,unregister:eV,getFieldState:ek,handleSubmit:eT,setError:eN,_subscribe:eA,_runSchema:X,_focusError:eC,_getWatch:ei,_getDirty:er,_setValid:E,_setFieldArray:(e,t=[],l,n,o=!0,u=!0)=>{if(n&&l&&!r.disabled){if(f.action=!0,u&&Array.isArray(w(a,e))){let t=l(w(a,e),n.argA,n.argB);o&&_(a,e,t)}if(u&&Array.isArray(w(s.errors,e))){let t=l(w(s.errors,e),n.argA,n.argB);o&&_(s.errors,e,t),ep(s.errors,e)}if((b.touchedFields||N.touchedFields)&&u&&Array.isArray(w(s.touchedFields,e))){let t=l(w(s.touchedFields,e),n.argA,n.argB);o&&_(s.touchedFields,e,t)}(b.dirtyFields||N.dirtyFields)&&(s.dirtyFields=J(i,d)),A.state.next({name:e,isDirty:er(e,t),dirtyFields:s.dirtyFields,errors:s.errors,isValid:s.isValid})}else _(d,e,t)},_setDisabledField:eF,_setErrors:e=>{s.errors=e,A.state.next({errors:s.errors,isValid:!1})},_getFieldArray:e=>g(w(f.mount?d:i,e,r.shouldUnregister?w(i,e,[]):[])),_reset:eE,_resetDefaultValues:()=>R(r.defaultValues)&&r.defaultValues().then(e=>{eO(e,r.resetOptions),A.state.next({isLoading:!1})}),_removeUnmounted:()=>{for(let e of p.unMount){let t=w(a,e);t&&(t._f.refs?t._f.refs.every(e=>!W(e)):!W(t._f.ref))&&eV(e)}p.unMount=new Set},_disableForm:e=>{j(e)&&(A.state.next({disabled:e}),ed(a,(t,r)=>{let s=w(a,r);s&&(t.disabled=s._f.disabled||e,Array.isArray(s._f.refs)&&s._f.refs.forEach(t=>{t.disabled=s._f.disabled||e}))},0,!1))},_subjects:A,_proxyFormState:b,get _fields(){return a},get _formValues(){return d},get _state(){return f},set _state(value){f=value},get _defaultValues(){return i},get _names(){return p},set _names(value){p=value},get _formState(){return s},get _options(){return r},set _options(value){r={...r,...value}}},subscribe:e=>(f.mount=!0,N={...N,...e.formState},eA({...e,formState:N})),trigger:e_,register:eD,handleSubmit:eT,watch:(e,t)=>R(e)?A.state.subscribe({next:r=>"values"in r&&e(ei(void 0,t),r)}):ei(e,t,!0),setValue:ev,getValues:eS,reset:eO,resetField:(e,t={})=>{w(a,e)&&(v(t.defaultValue)?ev(e,y(w(i,e))):(ev(e,t.defaultValue),_(i,e,y(t.defaultValue))),t.keepTouched||z(s.touchedFields,e),t.keepDirty||(z(s.dirtyFields,e),s.isDirty=t.defaultValue?er(e,y(w(i,e))):er()),!t.keepError&&(z(s.errors,e),b.isValid&&E()),A.state.next({...s}))},clearErrors:e=>{e&&O(e).forEach(e=>z(s.errors,e)),A.state.next({errors:e?s.errors:{}})},unregister:eV,setError:eN,setFocus:(e,t={})=>{let r=w(a,e),s=r&&r._f;if(s){let e=s.refs?s.refs[0]:s.ref;e.focus&&(e.focus(),t.shouldSelect&&R(e.select)&&e.select())}},getFieldState:ek};return{...eP,formControl:eP}}(e);t.current={...a,formState:s}}}let d=t.current.control;return d._options=e,V(()=>{let e=d._subscribe({formState:d._proxyFormState,callback:()=>i({...d._formState}),reRenderRoot:!0});return i(e=>({...e,isReady:!0})),d._formState.isReady=!0,e},[d]),a.useEffect(()=>d._disableForm(e.disabled),[d,e.disabled]),a.useEffect(()=>{e.mode&&(d._options.mode=e.mode),e.reValidateMode&&(d._options.reValidateMode=e.reValidateMode)},[d,e.mode,e.reValidateMode]),a.useEffect(()=>{e.errors&&(d._setErrors(e.errors),d._focusError())},[d,e.errors]),a.useEffect(()=>{e.shouldUnregister&&d._subjects.state.next({values:d._getWatch()})},[d,e.shouldUnregister]),a.useEffect(()=>{if(d._proxyFormState.isDirty){let e=d._getDirty();e!==s.isDirty&&d._subjects.state.next({isDirty:e})}},[d,s.isDirty]),a.useEffect(()=>{e.values&&!T(e.values,r.current)?(d._reset(e.values,{keepFieldsRef:!0,...d._options.resetOptions}),r.current=e.values,i(e=>({...e}))):d._resetDefaultValues()},[d,e.values]),a.useEffect(()=>{d._state.mount||(d._setValid(),d._state.mount=!0),d._state.watch&&(d._state.watch=!1,d._subjects.state.next({...d._formState})),d._removeUnmounted()}),t.current.formState=A(s,d),t.current}(),N=async t=>{r(!0);try{let r=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)}),s=await r.json();s.success?(f({title:"Login Successful",description:`Welcome back, ${s.data.user.first_name}!`}),e(s.data.user)):f({title:"Login Failed",description:s.error||"Invalid credentials",variant:"destructive"})}catch(e){console.error("Login error:",e),f({title:"Login Failed",description:"An error occurred. Please try again.",variant:"destructive"})}finally{r(!1)}};return(0,s.jsxs)("form",{onSubmit:x(N),className:"space-y-4",children:[(0,s.jsxs)("div",{className:"form-group",children:[s.jsx("label",{htmlFor:"email",className:"form-label",children:"Email Address"}),s.jsx("input",{id:"email",type:"email",className:"form-input",placeholder:"Enter your email",...p("email",{required:"Email is required",pattern:{value:/^[^\s@]+@[^\s@]+\.[^\s@]+$/,message:"Please enter a valid email address"}})}),b.email&&s.jsx("p",{className:"form-error",children:b.email.message})]}),(0,s.jsxs)("div",{className:"form-group",children:[s.jsx("label",{htmlFor:"password",className:"form-label",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[s.jsx("input",{id:"password",type:i?"text":"password",className:"form-input pr-10",placeholder:"Enter your password",...p("password",{required:"Password is required",minLength:{value:6,message:"Password must be at least 6 characters"}})}),s.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>d(!i),children:i?s.jsx(ej,{className:"h-4 w-4 text-muted-foreground"}):s.jsx(e_,{className:"h-4 w-4 text-muted-foreground"})})]}),b.password&&s.jsx("p",{className:"form-error",children:b.password.message})]}),s.jsx("button",{type:"submit",disabled:t,className:"w-full btn btn-primary btn-md flex items-center justify-center gap-2",children:t?s.jsx("div",{className:"spinner"}):(0,s.jsxs)(s.Fragment,{children:[s.jsx(eS,{className:"h-4 w-4"}),"Sign In"]})}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx("p",{className:"text-xs text-muted-foreground text-center",children:"Quick Login:"}),(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-2",children:[s.jsx("button",{type:"button",className:"btn btn-outline btn-sm text-xs",onClick:()=>{let e=document.querySelector("form"),t=e.querySelector("#email"),r=e.querySelector("#password");t.value="<EMAIL>",r.value="admin123"},children:"Admin"}),s.jsx("button",{type:"button",className:"btn btn-outline btn-sm text-xs",onClick:()=>{let e=document.querySelector("form"),t=e.querySelector("#email"),r=e.querySelector("#password");t.value="<EMAIL>",r.value="manager123"},children:"Manager"}),s.jsx("button",{type:"button",className:"btn btn-outline btn-sm text-xs",onClick:()=>{let e=document.querySelector("form"),t=e.querySelector("#email"),r=e.querySelector("#password");t.value="<EMAIL>",r.value="staff123"},children:"Staff"})]})]})]})}function eA(){let[e,t]=(0,a.useState)(null),[r,l]=(0,a.useState)(!0),n=(0,i.useRouter)();return r?s.jsx("div",{className:"min-h-screen flex items-center justify-center",children:s.jsx("div",{className:"spinner"})}):e?s.jsx("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("h1",{className:"text-2xl font-bold mb-4",children:["Welcome back, ",e.first_name,"!"]}),s.jsx("p",{className:"text-muted-foreground mb-4",children:"Redirecting to dashboard..."}),s.jsx("div",{className:"spinner mx-auto"})]})}):s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gold-50 to-silver-50 dark:from-gray-900 dark:to-gray-800",children:s.jsx("div",{className:"container mx-auto px-4 py-8",children:(0,s.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[s.jsx("div",{className:"gold-gradient w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center",children:s.jsx("svg",{className:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),s.jsx("h1",{className:"text-3xl font-bold text-foreground mb-2",children:"Jewellery Pro"}),s.jsx("p",{className:"text-muted-foreground",children:"Complete Business Management System"})]}),(0,s.jsxs)("div",{className:"card",children:[(0,s.jsxs)("div",{className:"card-header",children:[s.jsx("h2",{className:"card-title text-center",children:"Sign In"}),s.jsx("p",{className:"card-description text-center",children:"Enter your credentials to access your account"})]}),s.jsx("div",{className:"card-content",children:s.jsx(eN,{onSuccess:e=>{t(e),localStorage.setItem("currentUser",JSON.stringify(e)),n.push("/dashboard")}})})]}),(0,s.jsxs)("div",{className:"mt-6 p-4 bg-muted rounded-lg",children:[s.jsx("h3",{className:"text-sm font-medium mb-2",children:"Demo Credentials:"}),(0,s.jsxs)("div",{className:"text-xs text-muted-foreground space-y-1",children:[(0,s.jsxs)("div",{children:[s.jsx("strong",{children:"Admin:"})," <EMAIL> / admin123"]}),(0,s.jsxs)("div",{children:[s.jsx("strong",{children:"Manager:"})," <EMAIL> / manager123"]}),(0,s.jsxs)("div",{children:[s.jsx("strong",{children:"Staff:"})," <EMAIL> / staff123"]})]})]}),(0,s.jsxs)("div",{className:"mt-8 grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"text-center p-4 bg-card rounded-lg border",children:[s.jsx("div",{className:"w-8 h-8 mx-auto mb-2 text-primary",children:s.jsx("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"})})}),s.jsx("h3",{className:"text-sm font-medium",children:"Inventory"}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"Complete stock management"})]}),(0,s.jsxs)("div",{className:"text-center p-4 bg-card rounded-lg border",children:[s.jsx("div",{className:"w-8 h-8 mx-auto mb-2 text-primary",children:s.jsx("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"})})}),s.jsx("h3",{className:"text-sm font-medium",children:"Sales"}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"GST compliant billing"})]}),(0,s.jsxs)("div",{className:"text-center p-4 bg-card rounded-lg border",children:[s.jsx("div",{className:"w-8 h-8 mx-auto mb-2 text-primary",children:s.jsx("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})})}),s.jsx("h3",{className:"text-sm font-medium",children:"Customers"}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"CRM with KYC"})]}),(0,s.jsxs)("div",{className:"text-center p-4 bg-card rounded-lg border",children:[s.jsx("div",{className:"w-8 h-8 mx-auto mb-2 text-primary",children:s.jsx("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})})}),s.jsx("h3",{className:"text-sm font-medium",children:"Reports"}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"Business analytics"})]})]})]})})})}},5813:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>i});var s=r(326);r(7577);var a=r(4831);function i({children:e,...t}){return s.jsx(a.f,{...t,children:e})}},7083:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>x});var s=r(326),a=r(7068),i=r(7577),l=r(4578),n=r(9360),o=r(4019),d=r(7863);let u=l.zt,c=i.forwardRef(({className:e,...t},r)=>s.jsx(l.l_,{ref:r,className:(0,d.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));c.displayName=l.l_.displayName;let f=(0,n.j)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),m=i.forwardRef(({className:e,variant:t,...r},a)=>s.jsx(l.fC,{ref:a,className:(0,d.cn)(f({variant:t}),e),...r}));m.displayName=l.fC.displayName,i.forwardRef(({className:e,...t},r)=>s.jsx(l.aU,{ref:r,className:(0,d.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=l.aU.displayName;let p=i.forwardRef(({className:e,...t},r)=>s.jsx(l.x8,{ref:r,className:(0,d.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:s.jsx(o.Z,{className:"h-4 w-4"})}));p.displayName=l.x8.displayName;let h=i.forwardRef(({className:e,...t},r)=>s.jsx(l.Dx,{ref:r,className:(0,d.cn)("text-sm font-semibold",e),...t}));h.displayName=l.Dx.displayName;let y=i.forwardRef(({className:e,...t},r)=>s.jsx(l.dk,{ref:r,className:(0,d.cn)("text-sm opacity-90",e),...t}));function x(){let{toasts:e}=(0,a.pm)();return(0,s.jsxs)(u,{children:[e.map(function({id:e,title:t,description:r,action:a,...i}){return(0,s.jsxs)(m,{...i,children:[(0,s.jsxs)("div",{className:"grid gap-1",children:[t&&s.jsx(h,{children:t}),r&&s.jsx(y,{children:r})]}),a,s.jsx(p,{})]},e)}),s.jsx(c,{})]})}y.displayName=l.dk.displayName},7068:(e,t,r)=>{"use strict";r.d(t,{pm:()=>f});var s=r(7577);let a=0,i=new Map,l=e=>{if(i.has(e))return;let t=setTimeout(()=>{i.delete(e),u({type:"REMOVE_TOAST",toastId:e})},1e6);i.set(e,t)},n=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?l(r):e.toasts.forEach(e=>{l(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},o=[],d={toasts:[]};function u(e){d=n(d,e),o.forEach(e=>{e(d)})}function c({...e}){let t=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>u({type:"DISMISS_TOAST",toastId:t});return u({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||r()}}}),{id:t,dismiss:r,update:e=>u({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function f(){let[e,t]=s.useState(d);return s.useEffect(()=>(o.push(t),()=>{let e=o.indexOf(t);e>-1&&o.splice(e,1)}),[e]),{...e,toast:c,dismiss:e=>u({type:"DISMISS_TOAST",toastId:e})}}},7863:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var s=r(1135),a=r(1009);function i(...e){return(0,a.m6)((0,s.W)(e))}},5047:(e,t,r)=>{"use strict";var s=r(7389);r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}})},9030:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,metadata:()=>d});var s=r(9510),a=r(7366),i=r.n(a);r(7272);var l=r(8570);let n=(0,l.createProxy)(String.raw`C:\proj\jewellery-pro\components\theme-provider.tsx#ThemeProvider`),o=(0,l.createProxy)(String.raw`C:\proj\jewellery-pro\components\ui\toaster.tsx#Toaster`),d={title:"Jewellery Pro - Business Management System",description:"Complete Indian Jewellery Business Management Software",keywords:"jewellery, jewelry, business, management, inventory, sales, gold, silver",authors:[{name:"Jewellery Pro Team"}],viewport:"width=device-width, initial-scale=1"};function u({children:e}){return s.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:s.jsx("body",{className:i().className,children:(0,s.jsxs)(n,{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:[e,s.jsx(o,{})]})})})}},908:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(8570).createProxy)(String.raw`C:\proj\jewellery-pro\app\page.tsx#default`)},7272:()=>{}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[276,945],()=>r(746));module.exports=s})();