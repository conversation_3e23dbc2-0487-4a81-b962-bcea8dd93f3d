-- Migration: Create inventory and sales tables
-- Version: 003
-- Description: Add comprehensive inventory management and sales tracking

-- Create inventory_items table
CREATE TABLE IF NOT EXISTS inventory_items (
    id VARCHAR(36) PRIMARY KEY,
    item_code VARCHAR(50) UNIQUE NOT NULL,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    category VARCHAR(100) NOT NULL,
    subcategory VARCHAR(100),
    metal_type VARCHAR(50) NOT NULL,
    purity VARCHAR(20) NOT NULL,
    weight DECIMAL(10,3) NOT NULL,
    making_charges DECIMAL(10,2) DEFAULT 0,
    stone_charges DECIMAL(10,2) DEFAULT 0,
    other_charges DECIMAL(10,2) DEFAULT 0,
    total_value DECIMAL(12,2) NOT NULL,
    current_stock INT DEFAULT 1,
    min_stock_level INT DEFAULT 1,
    max_stock_level INT DEFAULT 100,
    location VARCHAR(100),
    supplier_id VARCHAR(36),
    description TEXT,
    barcode VARCHAR(100),
    status ENUM('active', 'inactive', 'deleted') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_item_code (item_code),
    INDEX idx_category (category),
    INDEX idx_metal_type (metal_type),
    INDEX idx_status (status),
    INDEX idx_current_stock (current_stock)
);

-- Create sales table
CREATE TABLE IF NOT EXISTS sales (
    id VARCHAR(36) PRIMARY KEY,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id VARCHAR(36),
    customer_name VARCHAR(255) NOT NULL,
    customer_phone VARCHAR(20),
    subtotal DECIMAL(12,2) NOT NULL,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(12,2) NOT NULL,
    payment_method ENUM('cash', 'card', 'upi', 'bank_transfer', 'cheque') NOT NULL,
    payment_status ENUM('pending', 'partial', 'paid', 'refunded') DEFAULT 'pending',
    status ENUM('active', 'cancelled', 'deleted') DEFAULT 'active',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_invoice_number (invoice_number),
    INDEX idx_customer_id (customer_id),
    INDEX idx_payment_status (payment_status),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL
);

-- Create sale_items table
CREATE TABLE IF NOT EXISTS sale_items (
    id VARCHAR(36) PRIMARY KEY,
    sale_id VARCHAR(36) NOT NULL,
    item_id VARCHAR(36) NOT NULL,
    quantity INT NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) NOT NULL,
    discount DECIMAL(10,2) DEFAULT 0,
    total DECIMAL(12,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_sale_id (sale_id),
    INDEX idx_item_id (item_id),
    FOREIGN KEY (sale_id) REFERENCES sales(id) ON DELETE CASCADE,
    FOREIGN KEY (item_id) REFERENCES inventory_items(id) ON DELETE RESTRICT
);

-- Create suppliers table
CREATE TABLE IF NOT EXISTS suppliers (
    id VARCHAR(36) PRIMARY KEY,
    supplier_code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    contact_person VARCHAR(255),
    email VARCHAR(255),
    phone VARCHAR(20),
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    pincode VARCHAR(10),
    gst_number VARCHAR(20),
    status ENUM('active', 'inactive', 'deleted') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_supplier_code (supplier_code),
    INDEX idx_name (name),
    INDEX idx_status (status)
);

-- Create purchases table
CREATE TABLE IF NOT EXISTS purchases (
    id VARCHAR(36) PRIMARY KEY,
    purchase_number VARCHAR(50) UNIQUE NOT NULL,
    supplier_id VARCHAR(36) NOT NULL,
    total_amount DECIMAL(12,2) NOT NULL,
    payment_status ENUM('pending', 'partial', 'paid') DEFAULT 'pending',
    status ENUM('active', 'cancelled', 'deleted') DEFAULT 'active',
    notes TEXT,
    purchase_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_purchase_number (purchase_number),
    INDEX idx_supplier_id (supplier_id),
    INDEX idx_purchase_date (purchase_date),
    INDEX idx_status (status),
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE RESTRICT
);

-- Create purchase_items table
CREATE TABLE IF NOT EXISTS purchase_items (
    id VARCHAR(36) PRIMARY KEY,
    purchase_id VARCHAR(36) NOT NULL,
    item_id VARCHAR(36),
    item_name VARCHAR(255) NOT NULL,
    quantity INT NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) NOT NULL,
    total DECIMAL(12,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_purchase_id (purchase_id),
    INDEX idx_item_id (item_id),
    FOREIGN KEY (purchase_id) REFERENCES purchases(id) ON DELETE CASCADE,
    FOREIGN KEY (item_id) REFERENCES inventory_items(id) ON DELETE SET NULL
);

-- Create schemes table for gold/silver investment schemes
CREATE TABLE IF NOT EXISTS schemes (
    id VARCHAR(36) PRIMARY KEY,
    scheme_code VARCHAR(50) UNIQUE NOT NULL,
    customer_id VARCHAR(36) NOT NULL,
    scheme_type ENUM('gold', 'silver') NOT NULL,
    scheme_name VARCHAR(255) NOT NULL,
    duration_months INT NOT NULL,
    monthly_amount DECIMAL(10,2) NOT NULL,
    total_amount DECIMAL(12,2) NOT NULL,
    paid_amount DECIMAL(12,2) DEFAULT 0,
    start_date DATE NOT NULL,
    maturity_date DATE NOT NULL,
    status ENUM('active', 'completed', 'cancelled', 'defaulted') DEFAULT 'active',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_scheme_code (scheme_code),
    INDEX idx_customer_id (customer_id),
    INDEX idx_scheme_type (scheme_type),
    INDEX idx_status (status),
    INDEX idx_maturity_date (maturity_date),
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE RESTRICT
);

-- Create scheme_payments table
CREATE TABLE IF NOT EXISTS scheme_payments (
    id VARCHAR(36) PRIMARY KEY,
    scheme_id VARCHAR(36) NOT NULL,
    payment_date DATE NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('cash', 'card', 'upi', 'bank_transfer', 'cheque') NOT NULL,
    receipt_number VARCHAR(50),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_scheme_id (scheme_id),
    INDEX idx_payment_date (payment_date),
    FOREIGN KEY (scheme_id) REFERENCES schemes(id) ON DELETE CASCADE
);

-- Create repairs table
CREATE TABLE IF NOT EXISTS repairs (
    id VARCHAR(36) PRIMARY KEY,
    repair_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id VARCHAR(36) NOT NULL,
    item_description TEXT NOT NULL,
    repair_type VARCHAR(100) NOT NULL,
    estimated_cost DECIMAL(10,2),
    actual_cost DECIMAL(10,2),
    advance_paid DECIMAL(10,2) DEFAULT 0,
    status ENUM('received', 'in_progress', 'completed', 'delivered', 'cancelled') DEFAULT 'received',
    received_date DATE NOT NULL,
    promised_date DATE,
    completed_date DATE,
    delivered_date DATE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_repair_number (repair_number),
    INDEX idx_customer_id (customer_id),
    INDEX idx_status (status),
    INDEX idx_promised_date (promised_date),
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE RESTRICT
);

-- Insert sample inventory items
INSERT IGNORE INTO inventory_items (
    id, item_code, name, category, metal_type, purity, weight, 
    making_charges, stone_charges, other_charges, total_value, current_stock
) VALUES 
(UUID(), 'CHA-001', 'Gold Chain 22K', 'Chains', 'Gold', '22K', 15.500, 5000, 0, 500, 85000, 5),
(UUID(), 'RIN-001', 'Diamond Ring', 'Rings', 'Gold', '18K', 3.200, 8000, 115000, 2000, 125000, 2),
(UUID(), 'BRA-001', 'Silver Bracelet', 'Bracelets', 'Silver', '925', 25.000, 500, 0, 100, 3500, 8),
(UUID(), 'EAR-001', 'Gold Earrings', 'Earrings', 'Gold', '22K', 8.750, 3000, 0, 300, 45000, 3),
(UUID(), 'NEC-001', 'Pearl Necklace', 'Necklaces', 'Gold', '18K', 12.300, 6000, 25000, 1000, 75000, 1);

-- Insert sample suppliers
INSERT IGNORE INTO suppliers (
    id, supplier_code, name, contact_person, email, phone, address, city, state
) VALUES 
(UUID(), 'SUP-001', 'Golden Suppliers Ltd', 'Rajesh Kumar', '<EMAIL>', '+91 98765 43210', '123 Gold Street', 'Mumbai', 'Maharashtra'),
(UUID(), 'SUP-002', 'Silver Touch Enterprises', 'Priya Sharma', '<EMAIL>', '+91 87654 32109', '456 Silver Avenue', 'Delhi', 'Delhi'),
(UUID(), 'SUP-003', 'Diamond World', 'Amit Patel', '<EMAIL>', '+91 76543 21098', '789 Diamond Plaza', 'Surat', 'Gujarat');
