#!/usr/bin/env tsx

/**
 * Database Initialization Script
 * Initializes the database with schema and sample data
 */

import { config } from 'dotenv'
import { testConnection } from '../../lib/config/database'
import { runMigrations } from '../../lib/database/migration-runner'
import { seedDatabase } from './seed-database'

// Load environment variables
config()

async function initializeDatabase() {
  console.log('🚀 Initializing Jewellery Pro Database...')
  console.log('=====================================')
  
  try {
    // Test database connection
    console.log('📡 Testing database connection...')
    const isConnected = await testConnection()
    
    if (!isConnected) {
      console.error('❌ Database connection failed!')
      console.error('Please check your database configuration in .env file')
      process.exit(1)
    }
    
    console.log('✅ Database connection successful')
    
    // Run migrations
    console.log('\n📋 Running database migrations...')
    await runMigrations()
    
    // Seed database with sample data
    console.log('\n🌱 Seeding database with sample data...')
    await seedDatabase()
    
    console.log('\n🎉 Database initialization completed successfully!')
    console.log('=====================================')
    console.log('You can now start the application with: npm run dev')
    
  } catch (error) {
    console.error('❌ Database initialization failed:', error)
    process.exit(1)
  }
}

// Run if called directly
if (require.main === module) {
  initializeDatabase()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Fatal error:', error)
      process.exit(1)
    })
}

export { initializeDatabase }
