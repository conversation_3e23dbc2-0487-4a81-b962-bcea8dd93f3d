const { default: fetch } = require('node-fetch')

async function testLogin() {
  try {
    const response = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    })

    const data = await response.json()
    
    console.log('Status:', response.status)
    console.log('Response:', data)
    
    if (response.ok) {
      console.log('✅ Login successful!')
    } else {
      console.log('❌ Login failed!')
    }
  } catch (error) {
    console.error('❌ Error:', error.message)
  }
}

testLogin()
