{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}, "/dashboard": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/dashboard", "dataRoute": "/dashboard.rsc"}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "fab6b0dab3943be43cf697a583935c8f", "previewModeSigningKey": "f54da0a27ecb44ace18ca03c3b7ae007daba8d7a9b9e9108daf62575bb244c98", "previewModeEncryptionKey": "c6a15c8c3701c555156f5c18e7b0b5104692f5476c3772f76adffabab3f71dec"}}