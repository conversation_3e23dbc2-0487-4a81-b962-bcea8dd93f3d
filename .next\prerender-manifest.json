{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}, "/dashboard": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/dashboard", "dataRoute": "/dashboard.rsc"}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "9ddf058f6716e225c158d5f779d81e35", "previewModeSigningKey": "1bba1f07fabca42e77044a9fa0f43962f98f2d31efd2281abd126e08adaf8d0d", "previewModeEncryptionKey": "cb008b4d23f0e20c5e89210dd0a9d9efd3e47bda8da0c9f11d3422c2b40c6a99"}}