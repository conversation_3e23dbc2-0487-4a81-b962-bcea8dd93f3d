'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useToast } from '@/components/ui/simple-toast'
import {
  X,
  Save,
  Upload,
  Package
} from 'lucide-react'

interface AddItemDialogProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
}

interface ItemFormData {
  name: string
  category: string
  subcategory: string
  metal_type: string
  purity: string
  weight: number
  making_charges: number
  stone_charges: number
  other_charges: number
  description: string
  supplier_id: string
  min_stock_level: number
  location: string
  barcode: string
}

const categories = [
  'Rings', 'Necklaces', 'Earrings', 'Bracelets', 'Chains', 
  'Pendants', 'Bangles', 'Anklets', 'Nose Pins', 'Coins'
]

const metalTypes = ['Gold', 'Silver', 'Platinum', 'Diamond', 'Gemstone']
const purities = ['24K', '22K', '18K', '14K', '10K', '925', '999', 'VVS1', 'VVS2', 'VS1', 'VS2']

export function AddItemDialog({ isOpen, onClose, onSuccess }: AddItemDialogProps) {
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch
  } = useForm<ItemFormData>()

  const metalType = watch('metal_type')

  const onSubmit = async (data: ItemFormData) => {
    setIsLoading(true)
    
    try {
      const response = await fetch('/api/inventory/items', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: 'Item Added Successfully',
          description: `${data.name} has been added to inventory`,
        })
        reset()
        onSuccess()
        onClose()
      } else {
        toast({
          title: 'Failed to Add Item',
          description: result.error || 'An error occurred',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('Add item error:', error)
      toast({
        title: 'Error',
        description: 'Failed to add item. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <>
      {/* Overlay */}
      <div className="fixed inset-0 bg-black/50 z-50" onClick={onClose} />
      
      {/* Dialog */}
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center space-x-2">
                  <Package className="w-5 h-5" />
                  <span>Add New Item</span>
                </CardTitle>
                <CardDescription>Add a new item to your inventory</CardDescription>
              </div>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="w-4 h-4" />
              </Button>
            </div>
          </CardHeader>
          
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Item Name *</label>
                  <input
                    {...register('name', { required: 'Item name is required' })}
                    className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="e.g., Gold Chain 22K"
                  />
                  {errors.name && (
                    <p className="text-red-500 text-xs mt-1">{errors.name.message}</p>
                  )}
                </div>

                <div>
                  <label className="text-sm font-medium">Category *</label>
                  <select
                    {...register('category', { required: 'Category is required' })}
                    className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    <option value="">Select Category</option>
                    {categories.map(cat => (
                      <option key={cat} value={cat}>{cat}</option>
                    ))}
                  </select>
                  {errors.category && (
                    <p className="text-red-500 text-xs mt-1">{errors.category.message}</p>
                  )}
                </div>

                <div>
                  <label className="text-sm font-medium">Metal Type *</label>
                  <select
                    {...register('metal_type', { required: 'Metal type is required' })}
                    className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    <option value="">Select Metal Type</option>
                    {metalTypes.map(metal => (
                      <option key={metal} value={metal}>{metal}</option>
                    ))}
                  </select>
                  {errors.metal_type && (
                    <p className="text-red-500 text-xs mt-1">{errors.metal_type.message}</p>
                  )}
                </div>

                <div>
                  <label className="text-sm font-medium">Purity *</label>
                  <select
                    {...register('purity', { required: 'Purity is required' })}
                    className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    <option value="">Select Purity</option>
                    {purities.map(purity => (
                      <option key={purity} value={purity}>{purity}</option>
                    ))}
                  </select>
                  {errors.purity && (
                    <p className="text-red-500 text-xs mt-1">{errors.purity.message}</p>
                  )}
                </div>

                <div>
                  <label className="text-sm font-medium">Weight (grams) *</label>
                  <input
                    type="number"
                    step="0.001"
                    {...register('weight', { 
                      required: 'Weight is required',
                      min: { value: 0.001, message: 'Weight must be greater than 0' }
                    })}
                    className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="0.000"
                  />
                  {errors.weight && (
                    <p className="text-red-500 text-xs mt-1">{errors.weight.message}</p>
                  )}
                </div>

                <div>
                  <label className="text-sm font-medium">Making Charges (₹)</label>
                  <input
                    type="number"
                    step="0.01"
                    {...register('making_charges')}
                    className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="0.00"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium">Stone Charges (₹)</label>
                  <input
                    type="number"
                    step="0.01"
                    {...register('stone_charges')}
                    className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="0.00"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium">Other Charges (₹)</label>
                  <input
                    type="number"
                    step="0.01"
                    {...register('other_charges')}
                    className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="0.00"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium">Min Stock Level</label>
                  <input
                    type="number"
                    {...register('min_stock_level')}
                    className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="1"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium">Location</label>
                  <input
                    {...register('location')}
                    className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="e.g., Shelf A1"
                  />
                </div>
              </div>

              <div>
                <label className="text-sm font-medium">Description</label>
                <textarea
                  {...register('description')}
                  rows={3}
                  className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                  placeholder="Item description..."
                />
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end space-x-2 pt-4 border-t">
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                      Adding...
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      Add Item
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </>
  )
}
