"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/login/route";
exports.ids = ["app/api/auth/login/route"];
exports.modules = {

/***/ "mysql2/promise":
/*!*********************************!*\
  !*** external "mysql2/promise" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("mysql2/promise");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=C%3A%5Cproj%5Cjewellery-pro%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cjewellery-pro&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=C%3A%5Cproj%5Cjewellery-pro%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cjewellery-pro&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_jewellery_pro_app_api_auth_login_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/auth/login/route.ts */ \"(rsc)/./app/api/auth/login/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/login/route\",\n        pathname: \"/api/auth/login\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/login/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\api\\\\auth\\\\login\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_jewellery_pro_app_api_auth_login_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/auth/login/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=C%3A%5Cproj%5Cjewellery-pro%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cjewellery-pro&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/auth/login/route.ts":
/*!*************************************!*\
  !*** ./app/api/auth/login/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_config_database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/config/database */ \"(rsc)/./lib/config/database.ts\");\n\n\n\nasync function POST(request) {\n    try {\n        const { email, password } = await request.json();\n        // Validate input\n        if (!email || !password) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Email and password are required\"\n            }, {\n                status: 400\n            });\n        }\n        // Find user by email\n        const users = await (0,_lib_config_database__WEBPACK_IMPORTED_MODULE_2__.executeQuery)(\"SELECT * FROM users WHERE email = ? AND is_active = TRUE\", [\n            email\n        ]);\n        if (users.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Invalid credentials\"\n            }, {\n                status: 401\n            });\n        }\n        const user = users[0];\n        // Verify password\n        const isValidPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(password, user.password_hash);\n        if (!isValidPassword) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Invalid credentials\"\n            }, {\n                status: 401\n            });\n        }\n        // Update last login\n        await (0,_lib_config_database__WEBPACK_IMPORTED_MODULE_2__.executeQuery)(\"UPDATE users SET last_login = NOW() WHERE id = ?\", [\n            user.id\n        ]);\n        // Remove password hash from response\n        const { password_hash, ...userWithoutPassword } = user;\n        // Parse permissions if it's a string\n        if (typeof userWithoutPassword.permissions === \"string\") {\n            userWithoutPassword.permissions = JSON.parse(userWithoutPassword.permissions);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                user: userWithoutPassword\n            },\n            message: \"Login successful\"\n        });\n    } catch (error) {\n        console.error(\"Login error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/auth/login/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/config/database.ts":
/*!********************************!*\
  !*** ./lib/config/database.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   closePool: () => (/* binding */ closePool),\n/* harmony export */   databaseConfig: () => (/* binding */ databaseConfig),\n/* harmony export */   executeDDLQuery: () => (/* binding */ executeDDLQuery),\n/* harmony export */   executeQuery: () => (/* binding */ executeQuery),\n/* harmony export */   executeTransaction: () => (/* binding */ executeTransaction),\n/* harmony export */   getPool: () => (/* binding */ getPool),\n/* harmony export */   testConnection: () => (/* binding */ testConnection)\n/* harmony export */ });\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mysql2/promise */ \"mysql2/promise\");\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mysql2_promise__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var dotenv__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dotenv */ \"(rsc)/./node_modules/dotenv/lib/main.js\");\n/* harmony import */ var dotenv__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dotenv__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Load environment variables\n(0,dotenv__WEBPACK_IMPORTED_MODULE_1__.config)();\nconst databaseConfig = {\n    host: process.env.DB_HOST || \"localhost\",\n    port: parseInt(process.env.DB_PORT || \"3306\"),\n    user: process.env.DB_USER || \"root\",\n    password: process.env.DB_PASSWORD || \"\",\n    database: process.env.DB_NAME || \"jewellery_pro_db\",\n    connectionLimit: 10,\n    acquireTimeout: 60000,\n    timeout: 60000,\n    reconnect: true,\n    charset: \"utf8mb4\"\n};\n// Create connection pool\nlet pool = null;\nfunction getPool() {\n    if (!pool) {\n        pool = mysql2_promise__WEBPACK_IMPORTED_MODULE_0___default().createPool({\n            ...databaseConfig,\n            namedPlaceholders: true,\n            supportBigNumbers: true,\n            bigNumberStrings: true,\n            dateStrings: false,\n            debug: \"development\" === \"development\",\n            multipleStatements: false,\n            timezone: \"+00:00\"\n        });\n        // Handle pool events\n        pool.on(\"connection\", (connection)=>{\n            console.log(\"New database connection established as id \" + connection.threadId);\n        });\n        pool.on(\"error\", (err)=>{\n            console.error(\"Database pool error:\", err);\n            if (err.code === \"PROTOCOL_CONNECTION_LOST\") {\n                console.log(\"Recreating database pool...\");\n                pool = null;\n                getPool();\n            }\n        });\n    }\n    return pool;\n}\n// Test database connection\nasync function testConnection() {\n    try {\n        const pool = getPool();\n        const connection = await pool.getConnection();\n        await connection.ping();\n        connection.release();\n        console.log(\"Database connection successful\");\n        return true;\n    } catch (error) {\n        console.error(\"Database connection failed:\", error);\n        return false;\n    }\n}\n// Execute query with error handling\nasync function executeQuery(query, params = []) {\n    const pool = getPool();\n    try {\n        const [results] = await pool.execute(query, params);\n        return results;\n    } catch (error) {\n        console.error(\"Query execution error:\", error);\n        console.error(\"Query:\", query);\n        console.error(\"Params:\", params);\n        throw error;\n    }\n}\n// Execute DDL query (for CREATE TRIGGER, etc.) using regular query instead of prepared statement\nasync function executeDDLQuery(query) {\n    const pool = getPool();\n    try {\n        const [results] = await pool.query(query);\n        return results;\n    } catch (error) {\n        console.error(\"DDL Query execution error:\", error);\n        console.error(\"Query:\", query);\n        throw error;\n    }\n}\n// Execute transaction\nasync function executeTransaction(callback) {\n    const pool = getPool();\n    const connection = await pool.getConnection();\n    try {\n        await connection.beginTransaction();\n        const result = await callback(connection);\n        await connection.commit();\n        return result;\n    } catch (error) {\n        await connection.rollback();\n        throw error;\n    } finally{\n        connection.release();\n    }\n}\n// Close all connections\nasync function closePool() {\n    if (pool) {\n        await pool.end();\n        pool = null;\n        console.log(\"Database pool closed\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/config/database.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/dotenv","vendor-chunks/bcryptjs"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=C%3A%5Cproj%5Cjewellery-pro%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cjewellery-pro&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();