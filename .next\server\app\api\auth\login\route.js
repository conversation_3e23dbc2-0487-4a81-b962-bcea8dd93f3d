"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/login/route";
exports.ids = ["app/api/auth/login/route"];
exports.modules = {

/***/ "mysql2/promise":
/*!*********************************!*\
  !*** external "mysql2/promise" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("mysql2/promise");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=C%3A%5Cproj%5Cjewellery-pro%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cjewellery-pro&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=C%3A%5Cproj%5Cjewellery-pro%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cjewellery-pro&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_jewellery_pro_app_api_auth_login_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/auth/login/route.ts */ \"(rsc)/./app/api/auth/login/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/login/route\",\n        pathname: \"/api/auth/login\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/login/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\api\\\\auth\\\\login\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_jewellery_pro_app_api_auth_login_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/auth/login/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=C%3A%5Cproj%5Cjewellery-pro%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cjewellery-pro&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/auth/login/route.ts":
/*!*************************************!*\
  !*** ./app/api/auth/login/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_config_database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/config/database */ \"(rsc)/./lib/config/database.ts\");\n\n\n\nasync function POST(request) {\n    try {\n        const { email, password } = await request.json();\n        // Validate input\n        if (!email || !password) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Email and password are required\"\n            }, {\n                status: 400\n            });\n        }\n        // Find user by email\n        const users = await (0,_lib_config_database__WEBPACK_IMPORTED_MODULE_2__.executeQuery)(\"SELECT * FROM users WHERE email = ? AND is_active = TRUE\", [\n            email\n        ]);\n        if (users.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Invalid credentials\"\n            }, {\n                status: 401\n            });\n        }\n        const user = users[0];\n        // Verify password\n        const isValidPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(password, user.password_hash);\n        if (!isValidPassword) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Invalid credentials\"\n            }, {\n                status: 401\n            });\n        }\n        // Update last login\n        await (0,_lib_config_database__WEBPACK_IMPORTED_MODULE_2__.executeQuery)(\"UPDATE users SET last_login = NOW() WHERE id = ?\", [\n            user.id\n        ]);\n        // Remove password hash from response\n        const { password_hash, ...userWithoutPassword } = user;\n        // Parse permissions if it's a string\n        if (typeof userWithoutPassword.permissions === \"string\") {\n            userWithoutPassword.permissions = JSON.parse(userWithoutPassword.permissions);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                user: userWithoutPassword\n            },\n            message: \"Login successful\"\n        });\n    } catch (error) {\n        console.error(\"Login error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/auth/login/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/config/database.ts":
/*!********************************!*\
  !*** ./lib/config/database.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   closePool: () => (/* binding */ closePool),\n/* harmony export */   databaseConfig: () => (/* binding */ databaseConfig),\n/* harmony export */   executeDDLQuery: () => (/* binding */ executeDDLQuery),\n/* harmony export */   executeQuery: () => (/* binding */ executeQuery),\n/* harmony export */   executeTransaction: () => (/* binding */ executeTransaction),\n/* harmony export */   getPool: () => (/* binding */ getPool),\n/* harmony export */   testConnection: () => (/* binding */ testConnection)\n/* harmony export */ });\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mysql2/promise */ \"mysql2/promise\");\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mysql2_promise__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var dotenv__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dotenv */ \"(rsc)/./node_modules/dotenv/lib/main.js\");\n/* harmony import */ var dotenv__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dotenv__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Load environment variables\n(0,dotenv__WEBPACK_IMPORTED_MODULE_1__.config)();\nconst databaseConfig = {\n    host: process.env.DB_HOST || \"localhost\",\n    port: parseInt(process.env.DB_PORT || \"3306\"),\n    user: process.env.DB_USER || \"root\",\n    password: process.env.DB_PASSWORD || \"\",\n    database: process.env.DB_NAME || \"jewellery_pro_db\",\n    connectionLimit: 10,\n    acquireTimeout: 60000,\n    timeout: 60000,\n    reconnect: true,\n    charset: \"utf8mb4\"\n};\n// Create connection pool\nlet pool = null;\nfunction getPool() {\n    if (!pool) {\n        pool = mysql2_promise__WEBPACK_IMPORTED_MODULE_0___default().createPool({\n            ...databaseConfig,\n            namedPlaceholders: true,\n            supportBigNumbers: true,\n            bigNumberStrings: true,\n            dateStrings: false,\n            debug: \"development\" === \"development\",\n            multipleStatements: false,\n            timezone: \"+00:00\"\n        });\n        pool.on(\"connection\", (connection)=>{\n            console.log(\"New database connection established as id \" + connection.threadId);\n        });\n        pool.on(\"error\", (err)=>{\n            console.error(\"Database pool error:\", err);\n            if (err.code === \"PROTOCOL_CONNECTION_LOST\") {\n                console.log(\"Recreating database pool...\");\n                pool = null;\n                getPool();\n            }\n        });\n    }\n    return pool;\n}\n// Test database connection\nasync function testConnection() {\n    try {\n        const pool = getPool();\n        const connection = await pool.getConnection();\n        await connection.ping();\n        connection.release();\n        console.log(\"Database connection successful\");\n        return true;\n    } catch (error) {\n        console.error(\"Database connection failed:\", error);\n        return false;\n    }\n}\n// Execute query with error handling\nasync function executeQuery(query, params = []) {\n    const pool = getPool();\n    try {\n        const [results] = await pool.execute(query, params);\n        return results;\n    } catch (error) {\n        console.error(\"Query execution error:\", error);\n        console.error(\"Query:\", query);\n        console.error(\"Params:\", params);\n        throw error;\n    }\n}\n// Execute DDL query (for CREATE TRIGGER, etc.) using regular query instead of prepared statement\nasync function executeDDLQuery(query) {\n    const pool = getPool();\n    try {\n        const [results] = await pool.query(query);\n        return results;\n    } catch (error) {\n        console.error(\"DDL Query execution error:\", error);\n        console.error(\"Query:\", query);\n        throw error;\n    }\n}\n// Execute transaction\nasync function executeTransaction(callback) {\n    const pool = getPool();\n    const connection = await pool.getConnection();\n    try {\n        await connection.beginTransaction();\n        const result = await callback(connection);\n        await connection.commit();\n        return result;\n    } catch (error) {\n        await connection.rollback();\n        throw error;\n    } finally{\n        connection.release();\n    }\n}\n// Close all connections\nasync function closePool() {\n    if (pool) {\n        await pool.end();\n        pool = null;\n        console.log(\"Database pool closed\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/config/database.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/dotenv","vendor-chunks/bcryptjs"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=C%3A%5Cproj%5Cjewellery-pro%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cjewellery-pro&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();