'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { User } from '@/lib/types'
import { DashboardLayout } from '@/components/dashboard/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { AddSchemeDialog } from '@/components/schemes/add-scheme-dialog'
import { useToast } from '@/components/ui/simple-toast'
import {
  TrendingUp,
  Plus,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Trash2,
  Calendar,
  DollarSign,
  Users,
  Clock
} from 'lucide-react'

export default function SchemesPage() {
  const [currentUser, setCurrentUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [schemes, setSchemes] = useState<any[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState('')
  const [filterStatus, setFilterStatus] = useState('')
  const router = useRouter()
  const { toast } = useToast()

  useEffect(() => {
    const savedUser = localStorage.getItem('currentUser')
    if (savedUser) {
      try {
        const user = JSON.parse(savedUser)
        setCurrentUser(user)
        loadSchemes()
      } catch (error) {
        console.error('Error parsing saved user:', error)
        localStorage.removeItem('currentUser')
        router.push('/')
      }
    } else {
      router.push('/')
    }
    setIsLoading(false)
  }, [router])

  const loadSchemes = async () => {
    try {
      const params = new URLSearchParams()
      if (filterType) params.append('scheme_type', filterType)
      if (filterStatus) params.append('status', filterStatus)

      const response = await fetch(`/api/schemes?${params}`)
      const result = await response.json()

      if (result.success) {
        setSchemes(result.data.schemes)
      }
    } catch (error) {
      console.error('Failed to load schemes:', error)
    }
  }

  const handleAddSuccess = () => {
    loadSchemes()
    toast({
      title: 'Success',
      description: 'Scheme created successfully',
    })
  }

  const handleCancelScheme = async (id: string) => {
    if (confirm('Are you sure you want to cancel this scheme?')) {
      try {
        const response = await fetch(`/api/schemes?id=${id}`, {
          method: 'DELETE'
        })

        const result = await response.json()

        if (result.success) {
          loadSchemes()
          toast({
            title: 'Scheme Cancelled',
            description: 'Scheme has been cancelled successfully',
          })
        }
      } catch (error) {
        console.error('Failed to cancel scheme:', error)
        toast({
          title: 'Error',
          description: 'Failed to cancel scheme',
          variant: 'destructive',
        })
      }
    }
  }

  const handleLogout = () => {
    localStorage.removeItem('currentUser')
    router.push('/')
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="spinner"></div>
      </div>
    )
  }

  if (!currentUser) {
    return null
  }

  // Filter schemes based on search and filters
  const filteredSchemes = schemes.filter(scheme => {
    const matchesSearch = !searchTerm ||
      scheme.scheme_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      scheme.customer_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      scheme.scheme_code?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesType = !filterType || scheme.scheme_type === filterType
    const matchesStatus = !filterStatus || scheme.status === filterStatus

    return matchesSearch && matchesType && matchesStatus
  })

  return (
    <DashboardLayout currentUser={currentUser} onLogout={handleLogout}>
      <div>
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">Schemes Management</h1>
            <p className="text-muted-foreground">Manage gold and silver investment schemes</p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
            <Button onClick={() => setIsAddDialogOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              New Scheme
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Schemes</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{schemes.filter(s => s.status === 'active').length}</div>
              <p className="text-xs text-muted-foreground">Currently running</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Value</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                ₹{schemes.reduce((sum, s) => sum + (s.total_amount || 0), 0).toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">All schemes combined</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Gold Schemes</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{schemes.filter(s => s.scheme_type === 'gold').length}</div>
              <p className="text-xs text-muted-foreground">Gold investments</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Maturing Soon</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">
                {schemes.filter(s => s.days_to_maturity <= 30 && s.days_to_maturity > 0).length}
              </div>
              <p className="text-xs text-muted-foreground">Within 30 days</p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Search & Filter</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <input
                    type="text"
                    placeholder="Search schemes..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                  />
                </div>
              </div>
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="">All Types</option>
                <option value="gold">Gold</option>
                <option value="silver">Silver</option>
              </select>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
              <Button variant="outline">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Schemes Table */}
        <Card>
          <CardHeader>
            <CardTitle>Investment Schemes</CardTitle>
            <CardDescription>Manage customer investment schemes and payments</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4">Scheme Code</th>
                    <th className="text-left py-3 px-4">Customer</th>
                    <th className="text-left py-3 px-4">Type</th>
                    <th className="text-left py-3 px-4">Duration</th>
                    <th className="text-left py-3 px-4">Monthly Amount</th>
                    <th className="text-left py-3 px-4">Progress</th>
                    <th className="text-left py-3 px-4">Status</th>
                    <th className="text-left py-3 px-4">Maturity</th>
                    <th className="text-left py-3 px-4">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredSchemes.length > 0 ? (
                    filteredSchemes.map((scheme) => (
                      <tr key={scheme.id} className="border-b hover:bg-muted/50">
                        <td className="py-3 px-4 font-medium">{scheme.scheme_code}</td>
                        <td className="py-3 px-4">
                          <div>
                            <div className="font-medium">{scheme.customer_name}</div>
                            <div className="text-sm text-muted-foreground">{scheme.customer_phone}</div>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            scheme.scheme_type === 'gold'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {scheme.scheme_type?.toUpperCase()}
                          </span>
                        </td>
                        <td className="py-3 px-4">{scheme.duration_months} months</td>
                        <td className="py-3 px-4 font-medium">₹{scheme.monthly_amount?.toLocaleString()}</td>
                        <td className="py-3 px-4">
                          <div className="w-full bg-muted rounded-full h-2">
                            <div
                              className="bg-primary h-2 rounded-full"
                              style={{ width: `${Math.min(scheme.completion_percentage || 0, 100)}%` }}
                            ></div>
                          </div>
                          <div className="text-xs text-muted-foreground mt-1">
                            {scheme.completion_percentage?.toFixed(1)}% complete
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            scheme.status === 'active' ? 'bg-green-100 text-green-800' :
                            scheme.status === 'completed' ? 'bg-blue-100 text-blue-800' :
                            scheme.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {scheme.status}
                          </span>
                        </td>
                        <td className="py-3 px-4">
                          <div className="text-sm">
                            {new Date(scheme.maturity_date).toLocaleDateString()}
                          </div>
                          {scheme.days_to_maturity > 0 && (
                            <div className={`text-xs ${
                              scheme.days_to_maturity <= 30 ? 'text-yellow-600' : 'text-muted-foreground'
                            }`}>
                              {scheme.days_to_maturity} days left
                            </div>
                          )}
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center space-x-2">
                            <Button variant="ghost" size="sm">
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleCancelScheme(scheme.id)}
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={9} className="py-8 text-center text-muted-foreground">
                        {schemes.length === 0 ? 'No schemes found' : 'No schemes match your search'}
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Add Scheme Dialog */}
        <AddSchemeDialog
          isOpen={isAddDialogOpen}
          onClose={() => setIsAddDialogOpen(false)}
          onSuccess={handleAddSuccess}
        />
      </div>
    </DashboardLayout>
  )
}
