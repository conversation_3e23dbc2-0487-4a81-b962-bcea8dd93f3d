import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/config/database'
import { v4 as uuidv4 } from 'uuid'

// GET - Fetch all schemes
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || ''
    const scheme_type = searchParams.get('scheme_type') || ''

    const offset = (page - 1) * limit

    let whereClause = 'WHERE s.status != "deleted"'
    const params: any[] = []

    if (search) {
      whereClause += ' AND (s.scheme_number LIKE ? OR s.scheme_name LIKE ? OR c.name LIKE ?)'
      params.push(`%${search}%`, `%${search}%`, `%${search}%`)
    }

    if (status) {
      whereClause += ' AND s.status = ?'
      params.push(status)
    }

    if (scheme_type) {
      whereClause += ' AND s.scheme_type = ?'
      params.push(scheme_type)
    }

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM schemes s 
      LEFT JOIN customers c ON s.customer_id = c.id 
      ${whereClause}
    `
    const countResult = await executeQuery<any[]>(countQuery, params)
    const total = countResult[0].total

    // Get schemes with customer info and payment summary
    const schemesQuery = `
      SELECT 
        s.id, s.scheme_number, s.customer_id, s.scheme_type, s.scheme_name,
        s.duration_months, s.installment_amount, s.total_amount, s.paid_amount,
        s.start_date, s.maturity_date, s.status, s.notes, s.created_at, s.updated_at,
        c.name as customer_name,
        c.phone as customer_phone,
        c.email as customer_email,
        ROUND((s.paid_amount / s.total_amount) * 100, 2) as completion_percentage,
        DATEDIFF(s.maturity_date, CURDATE()) as days_to_maturity
      FROM schemes s 
      LEFT JOIN customers c ON s.customer_id = c.id 
      ${whereClause}
      ORDER BY s.created_at DESC
      LIMIT ${limit} OFFSET ${offset}
    `

    const schemes = await executeQuery<any[]>(schemesQuery, params)

    // Get payment history for each scheme
    for (const scheme of schemes) {
      const paymentsQuery = `
        SELECT COUNT(*) as payment_count, 
               COALESCE(SUM(amount), 0) as total_paid,
               MAX(payment_date) as last_payment_date
        FROM scheme_payments 
        WHERE scheme_id = ?
      `
      const payments = await executeQuery<any[]>(paymentsQuery, [scheme.id])
      scheme.payment_count = payments[0].payment_count
      scheme.last_payment_date = payments[0].last_payment_date
    }

    return NextResponse.json({
      success: true,
      data: {
        schemes,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    })

  } catch (error) {
    console.error('Get schemes error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch schemes' },
      { status: 500 }
    )
  }
}

// POST - Create new scheme
export async function POST(request: NextRequest) {
  try {
    const data = await request.json()
    
    const {
      customer_id,
      customer_name,
      customer_phone,
      scheme_type,
      scheme_name,
      duration_months,
      monthly_amount,
      total_amount,
      start_date,
      maturity_date,
      notes
    } = data

    // Validate required fields
    if (!customer_name || !scheme_type || !scheme_name || !duration_months || !monthly_amount || !start_date) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      )
    }

    const schemeId = uuidv4()
    
    // Generate scheme code
    const typePrefix = scheme_type.toUpperCase().substring(0, 2)
    const today = new Date()
    const year = today.getFullYear().toString().slice(-2)
    const month = String(today.getMonth() + 1).padStart(2, '0')
    const timestamp = Date.now().toString().slice(-4)
    const scheme_code = `${typePrefix}${year}${month}-${timestamp}`

    await executeQuery(`
      INSERT INTO schemes (
        id, scheme_code, customer_id, customer_name, customer_phone,
        scheme_type, scheme_name, duration_months, monthly_amount, total_amount,
        paid_amount, start_date, maturity_date, status, notes, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `, [
      schemeId, scheme_code, customer_id || null, customer_name, customer_phone || null,
      scheme_type, scheme_name, duration_months, monthly_amount, total_amount,
      0, start_date, maturity_date, 'active', notes || null
    ])

    return NextResponse.json({
      success: true,
      data: { id: schemeId, scheme_code },
      message: 'Scheme created successfully'
    })

  } catch (error) {
    console.error('Create scheme error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create scheme' },
      { status: 500 }
    )
  }
}

// PUT - Update scheme
export async function PUT(request: NextRequest) {
  try {
    const data = await request.json()
    const { id, ...updateData } = data

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Scheme ID is required' },
        { status: 400 }
      )
    }

    // Build dynamic update query
    const updateFields = []
    const params = []

    for (const [key, value] of Object.entries(updateData)) {
      if (value !== undefined && key !== 'id') {
        updateFields.push(`${key} = ?`)
        params.push(value)
      }
    }

    if (updateFields.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No fields to update' },
        { status: 400 }
      )
    }

    updateFields.push('updated_at = NOW()')
    params.push(id)

    const query = `UPDATE schemes SET ${updateFields.join(', ')} WHERE id = ?`
    
    await executeQuery(query, params)

    return NextResponse.json({
      success: true,
      message: 'Scheme updated successfully'
    })

  } catch (error) {
    console.error('Update scheme error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update scheme' },
      { status: 500 }
    )
  }
}

// DELETE - Delete scheme
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Scheme ID is required' },
        { status: 400 }
      )
    }

    // Soft delete - update status to 'cancelled'
    await executeQuery(
      'UPDATE schemes SET status = ?, updated_at = NOW() WHERE id = ?',
      ['cancelled', id]
    )

    return NextResponse.json({
      success: true,
      message: 'Scheme cancelled successfully'
    })

  } catch (error) {
    console.error('Delete scheme error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to cancel scheme' },
      { status: 500 }
    )
  }
}
