'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useToast } from '@/components/ui/simple-toast'
import {
  X,
  Save,
  TrendingUp,
  Calculator
} from 'lucide-react'

interface AddSchemeDialogProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
}

interface SchemeFormData {
  customer_id: string
  customer_name: string
  customer_phone: string
  scheme_type: 'gold' | 'silver'
  scheme_name: string
  duration_months: number
  monthly_amount: number
  start_date: string
  notes: string
}

export function AddSchemeDialog({ isOpen, onClose, onSuccess }: AddSchemeDialogProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [customers, setCustomers] = useState<any[]>([])
  const [selectedCustomer, setSelectedCustomer] = useState<any>(null)
  const { toast } = useToast()
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue
  } = useForm<SchemeFormData>()

  const schemeType = watch('scheme_type')
  const durationMonths = watch('duration_months')
  const monthlyAmount = watch('monthly_amount')

  useEffect(() => {
    if (isOpen) {
      loadCustomers()
    }
  }, [isOpen])

  const loadCustomers = async () => {
    try {
      const response = await fetch('/api/customers')
      const result = await response.json()
      if (result.success) {
        setCustomers(result.data.customers)
      }
    } catch (error) {
      console.error('Failed to load customers:', error)
    }
  }

  const calculateTotalAmount = () => {
    if (durationMonths && monthlyAmount) {
      return durationMonths * monthlyAmount
    }
    return 0
  }

  const calculateMaturityDate = (startDate: string, months: number) => {
    if (!startDate || !months) return ''
    const start = new Date(startDate)
    start.setMonth(start.getMonth() + months)
    return start.toISOString().split('T')[0]
  }

  const onSubmit = async (data: SchemeFormData) => {
    setIsLoading(true)
    
    try {
      const totalAmount = calculateTotalAmount()
      const maturityDate = calculateMaturityDate(data.start_date, data.duration_months)
      
      const schemeData = {
        ...data,
        total_amount: totalAmount,
        maturity_date: maturityDate
      }

      const response = await fetch('/api/schemes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(schemeData),
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: 'Scheme Created Successfully',
          description: `${data.scheme_name} has been created for ${data.customer_name}`,
        })
        reset()
        setSelectedCustomer(null)
        onSuccess()
        onClose()
      } else {
        toast({
          title: 'Failed to Create Scheme',
          description: result.error || 'An error occurred',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('Create scheme error:', error)
      toast({
        title: 'Error',
        description: 'Failed to create scheme. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleCustomerSelect = (customerId: string) => {
    const customer = customers.find(c => c.id === customerId)
    if (customer) {
      setSelectedCustomer(customer)
      setValue('customer_id', customer.id)
      setValue('customer_name', customer.name)
      setValue('customer_phone', customer.phone)
    }
  }

  if (!isOpen) return null

  const totalAmount = calculateTotalAmount()

  return (
    <>
      {/* Overlay */}
      <div className="fixed inset-0 bg-black/50 z-50" onClick={onClose} />
      
      {/* Dialog */}
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="w-5 h-5" />
                  <span>Create New Scheme</span>
                </CardTitle>
                <CardDescription>Set up a new gold or silver investment scheme</CardDescription>
              </div>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="w-4 h-4" />
              </Button>
            </div>
          </CardHeader>
          
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Customer Selection */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Customer Information</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Select Customer</label>
                    <select
                      onChange={(e) => handleCustomerSelect(e.target.value)}
                      className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    >
                      <option value="">Select existing customer</option>
                      {customers.map(customer => (
                        <option key={customer.id} value={customer.id}>
                          {customer.name} - {customer.phone}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Or Enter New Customer</label>
                    <input
                      {...register('customer_name', { required: 'Customer name is required' })}
                      className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                      placeholder="Customer name"
                      value={selectedCustomer?.name || ''}
                      onChange={(e) => {
                        setValue('customer_name', e.target.value)
                        if (e.target.value !== selectedCustomer?.name) {
                          setSelectedCustomer(null)
                          setValue('customer_id', '')
                        }
                      }}
                    />
                    {errors.customer_name && (
                      <p className="text-red-500 text-xs mt-1">{errors.customer_name.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="text-sm font-medium">Phone Number</label>
                    <input
                      {...register('customer_phone')}
                      className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                      placeholder="Customer phone"
                      value={selectedCustomer?.phone || ''}
                      onChange={(e) => setValue('customer_phone', e.target.value)}
                    />
                  </div>
                </div>
              </div>

              {/* Scheme Details */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Scheme Details</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Scheme Type *</label>
                    <select
                      {...register('scheme_type', { required: 'Scheme type is required' })}
                      className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    >
                      <option value="">Select Type</option>
                      <option value="gold">Gold Scheme</option>
                      <option value="silver">Silver Scheme</option>
                    </select>
                    {errors.scheme_type && (
                      <p className="text-red-500 text-xs mt-1">{errors.scheme_type.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="text-sm font-medium">Scheme Name *</label>
                    <input
                      {...register('scheme_name', { required: 'Scheme name is required' })}
                      className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                      placeholder={`e.g., ${schemeType === 'gold' ? 'Gold Savings Plan' : 'Silver Investment Plan'}`}
                    />
                    {errors.scheme_name && (
                      <p className="text-red-500 text-xs mt-1">{errors.scheme_name.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="text-sm font-medium">Duration (Months) *</label>
                    <select
                      {...register('duration_months', { 
                        required: 'Duration is required',
                        valueAsNumber: true 
                      })}
                      className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    >
                      <option value="">Select Duration</option>
                      <option value="6">6 Months</option>
                      <option value="12">12 Months (1 Year)</option>
                      <option value="18">18 Months</option>
                      <option value="24">24 Months (2 Years)</option>
                      <option value="36">36 Months (3 Years)</option>
                      <option value="60">60 Months (5 Years)</option>
                    </select>
                    {errors.duration_months && (
                      <p className="text-red-500 text-xs mt-1">{errors.duration_months.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="text-sm font-medium">Monthly Amount (₹) *</label>
                    <input
                      type="number"
                      step="100"
                      {...register('monthly_amount', { 
                        required: 'Monthly amount is required',
                        valueAsNumber: true,
                        min: { value: 500, message: 'Minimum amount is ₹500' }
                      })}
                      className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                      placeholder="5000"
                    />
                    {errors.monthly_amount && (
                      <p className="text-red-500 text-xs mt-1">{errors.monthly_amount.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="text-sm font-medium">Start Date *</label>
                    <input
                      type="date"
                      {...register('start_date', { required: 'Start date is required' })}
                      className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                      min={new Date().toISOString().split('T')[0]}
                    />
                    {errors.start_date && (
                      <p className="text-red-500 text-xs mt-1">{errors.start_date.message}</p>
                    )}
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium">Notes</label>
                  <textarea
                    {...register('notes')}
                    rows={3}
                    className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="Additional notes about the scheme..."
                  />
                </div>
              </div>

              {/* Calculation Summary */}
              {totalAmount > 0 && (
                <div className="bg-muted p-4 rounded-lg">
                  <h4 className="font-medium mb-3 flex items-center">
                    <Calculator className="w-4 h-4 mr-2" />
                    Scheme Summary
                  </h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Monthly Payment:</span>
                      <div className="font-medium">₹{monthlyAmount?.toLocaleString()}</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Duration:</span>
                      <div className="font-medium">{durationMonths} months</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Total Amount:</span>
                      <div className="font-bold text-lg">₹{totalAmount.toLocaleString()}</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Maturity Date:</span>
                      <div className="font-medium">
                        {watch('start_date') ? calculateMaturityDate(watch('start_date'), durationMonths) : 'Select start date'}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex justify-end space-x-2 pt-4 border-t">
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      Create Scheme
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </>
  )
}
