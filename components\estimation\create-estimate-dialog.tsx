'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useToast } from '@/components/ui/simple-toast'
import {
  X,
  Save,
  Calculator,
  FileText,
  Printer,
  Lock
} from 'lucide-react'

interface CreateEstimateDialogProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
}

interface EstimateItem {
  id: string
  item_name: string
  metal_type: string
  purity: string
  gross_weight: number
  stone_weight: number
  net_weight: number
  metal_rate: number
  making_charges: number
  stone_charges: number
  wastage_percent: number
  wastage_amount: number
  other_charges: number
  total_amount: number
}

interface EstimateFormData {
  customer_name: string
  customer_phone: string
  customer_email: string
  valid_till: string
  rate_lock: boolean
  notes: string
  terms_conditions: string
}

export function CreateEstimateDialog({ isOpen, onClose, onSuccess }: CreateEstimateDialogProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [estimateItems, setEstimateItems] = useState<EstimateItem[]>([])
  const [metalRates, setMetalRates] = useState<any>({})
  const [newItem, setNewItem] = useState({
    item_name: '',
    metal_type: 'gold',
    purity: '22k',
    gross_weight: 0,
    stone_weight: 0,
    making_charges: 0,
    stone_charges: 0,
    wastage_percent: 2,
    other_charges: 0
  })
  const { toast } = useToast()
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch
  } = useForm<EstimateFormData>()

  useEffect(() => {
    if (isOpen) {
      loadMetalRates()
      // Set default valid till date (7 days from now)
      const validTill = new Date()
      validTill.setDate(validTill.getDate() + 7)
      reset({
        valid_till: validTill.toISOString().split('T')[0]
      })
    }
  }, [isOpen, reset])

  const loadMetalRates = async () => {
    try {
      const response = await fetch('/api/metal-rates/current')
      const result = await response.json()
      if (result.success) {
        setMetalRates(result.data)
      }
    } catch (error) {
      console.error('Failed to load metal rates:', error)
      // Default rates
      setMetalRates({
        'gold_24k': 6800,
        'gold_22k': 6200,
        'gold_18k': 4800,
        'silver_999': 85,
        'silver_925': 78
      })
    }
  }

  const calculateItemTotal = (item: any) => {
    const metalRateKey = `${item.metal_type.toLowerCase()}_${item.purity.toLowerCase()}`
    const currentRate = metalRates[metalRateKey] || 0
    
    const netWeight = item.gross_weight - item.stone_weight
    const metalValue = netWeight * currentRate
    const wastageAmount = (metalValue * item.wastage_percent) / 100
    const totalAmount = metalValue + wastageAmount + item.making_charges + item.stone_charges + item.other_charges
    
    return {
      metal_rate: currentRate,
      net_weight: netWeight,
      wastage_amount: wastageAmount,
      total_amount: totalAmount
    }
  }

  const addItemToEstimate = () => {
    if (!newItem.item_name || !newItem.gross_weight) {
      toast({
        title: 'Missing Information',
        description: 'Please enter item name and weight',
        variant: 'destructive',
      })
      return
    }

    const calculations = calculateItemTotal(newItem)
    
    const estimateItem: EstimateItem = {
      id: Date.now().toString(),
      ...newItem,
      ...calculations
    }

    setEstimateItems([...estimateItems, estimateItem])
    
    // Reset form
    setNewItem({
      item_name: '',
      metal_type: 'gold',
      purity: '22k',
      gross_weight: 0,
      stone_weight: 0,
      making_charges: 0,
      stone_charges: 0,
      wastage_percent: 2,
      other_charges: 0
    })
  }

  const removeEstimateItem = (id: string) => {
    setEstimateItems(items => items.filter(item => item.id !== id))
  }

  const calculateTotals = () => {
    const subtotal = estimateItems.reduce((sum, item) => sum + item.total_amount, 0)
    return { subtotal, grandTotal: subtotal }
  }

  const onSubmit = async (data: EstimateFormData) => {
    if (estimateItems.length === 0) {
      toast({
        title: 'No Items',
        description: 'Please add at least one item to the estimate',
        variant: 'destructive',
      })
      return
    }

    setIsLoading(true)
    
    try {
      const { grandTotal } = calculateTotals()
      
      const estimateData = {
        ...data,
        items: estimateItems,
        total_amount: grandTotal,
        metal_rates_locked: data.rate_lock ? metalRates : null
      }

      const response = await fetch('/api/estimates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(estimateData),
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: 'Estimate Created',
          description: `Estimate ${result.data.estimate_number} created successfully`,
        })
        reset()
        setEstimateItems([])
        onSuccess()
        onClose()
      } else {
        toast({
          title: 'Failed to Create Estimate',
          description: result.error || 'An error occurred',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('Create estimate error:', error)
      toast({
        title: 'Error',
        description: 'Failed to create estimate. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (!isOpen) return null

  const { subtotal, grandTotal } = calculateTotals()

  return (
    <>
      {/* Overlay */}
      <div className="fixed inset-0 bg-black/50 z-50" onClick={onClose} />
      
      {/* Dialog */}
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-5xl max-h-[90vh] overflow-y-auto">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center space-x-2">
                  <Calculator className="w-5 h-5" />
                  <span>Create Estimate</span>
                </CardTitle>
                <CardDescription>Create jewelry estimate with current metal rates</CardDescription>
              </div>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="w-4 h-4" />
              </Button>
            </div>
          </CardHeader>
          
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Customer Information */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium">Customer Name *</label>
                  <input
                    {...register('customer_name', { required: 'Customer name is required' })}
                    className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="Customer name"
                  />
                  {errors.customer_name && (
                    <p className="text-red-500 text-xs mt-1">{errors.customer_name.message}</p>
                  )}
                </div>

                <div>
                  <label className="text-sm font-medium">Phone Number</label>
                  <input
                    {...register('customer_phone')}
                    className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="Phone number"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium">Email</label>
                  <input
                    type="email"
                    {...register('customer_email')}
                    className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="Email address"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium">Valid Till *</label>
                  <input
                    type="date"
                    {...register('valid_till', { required: 'Valid till date is required' })}
                    className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    min={new Date().toISOString().split('T')[0]}
                  />
                  {errors.valid_till && (
                    <p className="text-red-500 text-xs mt-1">{errors.valid_till.message}</p>
                  )}
                </div>

                <div className="flex items-center space-x-2 mt-6">
                  <input
                    type="checkbox"
                    {...register('rate_lock')}
                    className="rounded"
                  />
                  <label className="text-sm font-medium flex items-center">
                    <Lock className="w-4 h-4 mr-1" />
                    Lock Current Rates
                  </label>
                </div>
              </div>

              {/* Add Items Section */}
              <div className="border rounded-lg p-4">
                <h3 className="text-lg font-medium mb-4">Add Items</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                  <div>
                    <label className="text-sm font-medium">Item Name</label>
                    <input
                      value={newItem.item_name}
                      onChange={(e) => setNewItem({...newItem, item_name: e.target.value})}
                      className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                      placeholder="e.g., Gold Chain"
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium">Metal Type</label>
                    <select
                      value={newItem.metal_type}
                      onChange={(e) => setNewItem({...newItem, metal_type: e.target.value})}
                      className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    >
                      <option value="gold">Gold</option>
                      <option value="silver">Silver</option>
                    </select>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Purity</label>
                    <select
                      value={newItem.purity}
                      onChange={(e) => setNewItem({...newItem, purity: e.target.value})}
                      className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    >
                      {newItem.metal_type === 'gold' ? (
                        <>
                          <option value="24k">24K</option>
                          <option value="22k">22K</option>
                          <option value="18k">18K</option>
                          <option value="14k">14K</option>
                        </>
                      ) : (
                        <>
                          <option value="999">999</option>
                          <option value="925">925</option>
                        </>
                      )}
                    </select>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Gross Weight (g)</label>
                    <input
                      type="number"
                      step="0.001"
                      value={newItem.gross_weight}
                      onChange={(e) => setNewItem({...newItem, gross_weight: parseFloat(e.target.value) || 0})}
                      className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                      placeholder="0.000"
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium">Stone Weight (g)</label>
                    <input
                      type="number"
                      step="0.001"
                      value={newItem.stone_weight}
                      onChange={(e) => setNewItem({...newItem, stone_weight: parseFloat(e.target.value) || 0})}
                      className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                      placeholder="0.000"
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium">Making Charges (₹)</label>
                    <input
                      type="number"
                      step="0.01"
                      value={newItem.making_charges}
                      onChange={(e) => setNewItem({...newItem, making_charges: parseFloat(e.target.value) || 0})}
                      className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                      placeholder="0.00"
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium">Stone Charges (₹)</label>
                    <input
                      type="number"
                      step="0.01"
                      value={newItem.stone_charges}
                      onChange={(e) => setNewItem({...newItem, stone_charges: parseFloat(e.target.value) || 0})}
                      className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                      placeholder="0.00"
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium">Wastage (%)</label>
                    <input
                      type="number"
                      step="0.1"
                      value={newItem.wastage_percent}
                      onChange={(e) => setNewItem({...newItem, wastage_percent: parseFloat(e.target.value) || 0})}
                      className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                      placeholder="2.0"
                    />
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button type="button" onClick={addItemToEstimate}>
                    <Calculator className="w-4 h-4 mr-2" />
                    Add Item
                  </Button>
                </div>

                {/* Items Table */}
                {estimateItems.length > 0 && (
                  <div className="mt-6 overflow-x-auto">
                    <table className="w-full border text-sm">
                      <thead>
                        <tr className="bg-muted">
                          <th className="text-left p-2">Item</th>
                          <th className="text-left p-2">Metal & Purity</th>
                          <th className="text-left p-2">Weight</th>
                          <th className="text-left p-2">Rate</th>
                          <th className="text-left p-2">Charges</th>
                          <th className="text-left p-2">Total</th>
                          <th className="text-left p-2">Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        {estimateItems.map(item => (
                          <tr key={item.id} className="border-t">
                            <td className="p-2 font-medium">{item.item_name}</td>
                            <td className="p-2">{item.metal_type} {item.purity}</td>
                            <td className="p-2">
                              <div className="text-xs">
                                <div>Gross: {item.gross_weight}g</div>
                                <div>Net: {item.net_weight}g</div>
                              </div>
                            </td>
                            <td className="p-2">₹{item.metal_rate}/g</td>
                            <td className="p-2">
                              <div className="text-xs">
                                <div>Making: ₹{item.making_charges}</div>
                                <div>Stone: ₹{item.stone_charges}</div>
                                <div>Wastage: ₹{item.wastage_amount.toFixed(2)}</div>
                              </div>
                            </td>
                            <td className="p-2 font-bold">₹{item.total_amount.toFixed(2)}</td>
                            <td className="p-2">
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => removeEstimateItem(item.id)}
                              >
                                <X className="w-4 h-4" />
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>

              {/* Estimate Summary */}
              {estimateItems.length > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium">Notes</label>
                      <textarea
                        {...register('notes')}
                        rows={3}
                        className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                        placeholder="Additional notes..."
                      />
                    </div>

                    <div>
                      <label className="text-sm font-medium">Terms & Conditions</label>
                      <textarea
                        {...register('terms_conditions')}
                        rows={3}
                        className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                        placeholder="Terms and conditions..."
                      />
                    </div>
                  </div>

                  <div className="bg-muted p-4 rounded-lg">
                    <h4 className="font-medium mb-4">Estimate Summary</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>Subtotal:</span>
                        <span>₹{subtotal.toFixed(2)}</span>
                      </div>
                      <hr />
                      <div className="flex justify-between font-bold text-lg">
                        <span>Total Amount:</span>
                        <span>₹{grandTotal.toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex justify-end space-x-2 pt-4 border-t">
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading || estimateItems.length === 0}>
                  {isLoading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      Create Estimate
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </>
  )
}
