'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { User } from '@/lib/types'
import { DashboardLayout } from '@/components/dashboard/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { AddRepairDialog } from '@/components/repairs/add-repair-dialog'
import { useToast } from '@/components/ui/simple-toast'
import {
  Wrench,
  Plus,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Trash2,
  Clock,
  CheckCircle,
  AlertCircle,
  Calendar
} from 'lucide-react'

export default function RepairsPage() {
  const [currentUser, setCurrentUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [repairs, setRepairs] = useState<any[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('')
  const router = useRouter()
  const { toast } = useToast()

  useEffect(() => {
    const savedUser = localStorage.getItem('currentUser')
    if (savedUser) {
      try {
        const user = JSON.parse(savedUser)
        setCurrentUser(user)
        loadRepairs()
      } catch (error) {
        console.error('Error parsing saved user:', error)
        localStorage.removeItem('currentUser')
        router.push('/')
      }
    } else {
      router.push('/')
    }
    setIsLoading(false)
  }, [router])

  const loadRepairs = async () => {
    try {
      const params = new URLSearchParams()
      if (filterStatus) params.append('status', filterStatus)

      const response = await fetch(`/api/repairs?${params}`)
      const result = await response.json()

      if (result.success) {
        setRepairs(result.data.repairs)
      }
    } catch (error) {
      console.error('Failed to load repairs:', error)
    }
  }

  const handleAddSuccess = () => {
    loadRepairs()
    toast({
      title: 'Success',
      description: 'Repair job created successfully',
    })
  }

  const handleStatusUpdate = async (id: string, status: string) => {
    try {
      const response = await fetch('/api/repairs', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id, status }),
      })

      const result = await response.json()

      if (result.success) {
        loadRepairs()
        toast({
          title: 'Status Updated',
          description: `Repair job status updated to ${status}`,
        })
      }
    } catch (error) {
      console.error('Failed to update status:', error)
      toast({
        title: 'Error',
        description: 'Failed to update status',
        variant: 'destructive',
      })
    }
  }

  const handleLogout = () => {
    localStorage.removeItem('currentUser')
    router.push('/')
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="spinner"></div>
      </div>
    )
  }

  if (!currentUser) {
    return null
  }

  // Filter repairs based on search and status
  const filteredRepairs = repairs.filter(repair => {
    const matchesSearch = !searchTerm ||
      repair.repair_number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      repair.customer_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      repair.item_description?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = !filterStatus || repair.status === filterStatus

    return matchesSearch && matchesStatus
  })

  return (
    <DashboardLayout currentUser={currentUser} onLogout={handleLogout}>
      <div>
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">Repairs Management</h1>
            <p className="text-muted-foreground">Manage jewelry repair jobs and tracking</p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
            <Button onClick={() => setIsAddDialogOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              New Repair Job
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Jobs</CardTitle>
              <Wrench className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {repairs.filter(r => ['received', 'in_progress'].includes(r.status)).length}
              </div>
              <p className="text-xs text-muted-foreground">In progress</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {repairs.filter(r => r.status === 'completed').length}
              </div>
              <p className="text-xs text-muted-foreground">Ready for pickup</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Overdue</CardTitle>
              <AlertCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {repairs.filter(r => r.days_to_promised < 0 && !['completed', 'delivered', 'cancelled'].includes(r.status)).length}
              </div>
              <p className="text-xs text-muted-foreground">Past promised date</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">This Month</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {repairs.filter(r => {
                  const receivedDate = new Date(r.received_date)
                  const now = new Date()
                  return receivedDate.getMonth() === now.getMonth() && receivedDate.getFullYear() === now.getFullYear()
                }).length}
              </div>
              <p className="text-xs text-muted-foreground">New repairs</p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Search & Filter</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <input
                    type="text"
                    placeholder="Search repair jobs..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                  />
                </div>
              </div>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="">All Status</option>
                <option value="received">Received</option>
                <option value="in_progress">In Progress</option>
                <option value="completed">Completed</option>
                <option value="delivered">Delivered</option>
                <option value="cancelled">Cancelled</option>
              </select>
              <Button variant="outline">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Repairs Table */}
        <Card>
          <CardHeader>
            <CardTitle>Repair Jobs</CardTitle>
            <CardDescription>Track and manage jewelry repair jobs</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4">Job #</th>
                    <th className="text-left py-3 px-4">Customer</th>
                    <th className="text-left py-3 px-4">Item & Repair</th>
                    <th className="text-left py-3 px-4">Cost</th>
                    <th className="text-left py-3 px-4">Status</th>
                    <th className="text-left py-3 px-4">Timeline</th>
                    <th className="text-left py-3 px-4">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredRepairs.length > 0 ? (
                    filteredRepairs.map((repair) => (
                      <tr key={repair.id} className="border-b hover:bg-muted/50">
                        <td className="py-3 px-4 font-medium">{repair.repair_number}</td>
                        <td className="py-3 px-4">
                          <div>
                            <div className="font-medium">{repair.customer_name}</div>
                            <div className="text-sm text-muted-foreground">{repair.customer_phone}</div>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <div>
                            <div className="font-medium">{repair.repair_type}</div>
                            <div className="text-sm text-muted-foreground line-clamp-2">
                              {repair.item_description}
                            </div>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <div>
                            {repair.estimated_cost && (
                              <div className="text-sm">Est: ₹{repair.estimated_cost.toLocaleString()}</div>
                            )}
                            {repair.advance_paid > 0 && (
                              <div className="text-sm text-green-600">Adv: ₹{repair.advance_paid.toLocaleString()}</div>
                            )}
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <select
                            value={repair.status}
                            onChange={(e) => handleStatusUpdate(repair.id, e.target.value)}
                            className={`px-2 py-1 rounded text-xs border-0 ${
                              repair.status === 'received' ? 'bg-blue-100 text-blue-800' :
                              repair.status === 'in_progress' ? 'bg-yellow-100 text-yellow-800' :
                              repair.status === 'completed' ? 'bg-green-100 text-green-800' :
                              repair.status === 'delivered' ? 'bg-purple-100 text-purple-800' :
                              'bg-red-100 text-red-800'
                            }`}
                          >
                            <option value="received">Received</option>
                            <option value="in_progress">In Progress</option>
                            <option value="completed">Completed</option>
                            <option value="delivered">Delivered</option>
                            <option value="cancelled">Cancelled</option>
                          </select>
                        </td>
                        <td className="py-3 px-4">
                          <div className="text-sm">
                            <div>Received: {new Date(repair.received_date).toLocaleDateString()}</div>
                            {repair.promised_date && (
                              <div className={`${
                                repair.days_to_promised < 0 ? 'text-red-600' :
                                repair.days_to_promised <= 2 ? 'text-yellow-600' :
                                'text-muted-foreground'
                              }`}>
                                Promise: {new Date(repair.promised_date).toLocaleDateString()}
                                {repair.days_to_promised !== null && (
                                  <span className="ml-1">
                                    ({repair.days_to_promised < 0 ? 'Overdue' : `${repair.days_to_promised}d left`})
                                  </span>
                                )}
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center space-x-2">
                            <Button variant="ghost" size="sm">
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleStatusUpdate(repair.id, 'cancelled')}
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={7} className="py-8 text-center text-muted-foreground">
                        {repairs.length === 0 ? 'No repair jobs found' : 'No repairs match your search'}
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Add Repair Dialog */}
        <AddRepairDialog
          isOpen={isAddDialogOpen}
          onClose={() => setIsAddDialogOpen(false)}
          onSuccess={handleAddSuccess}
        />
      </div>
    </DashboardLayout>
  )
}
