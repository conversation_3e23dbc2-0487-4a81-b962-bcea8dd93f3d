import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/config/database'
import { v4 as uuidv4 } from 'uuid'

// GET - Fetch all repairs
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || ''

    const offset = (page - 1) * limit

    let whereClause = 'WHERE r.status != "deleted"'
    const params: any[] = []

    if (search) {
      whereClause += ' AND (r.repair_number LIKE ? OR r.item_description LIKE ? OR c.name LIKE ?)'
      params.push(`%${search}%`, `%${search}%`, `%${search}%`)
    }

    if (status) {
      whereClause += ' AND r.status = ?'
      params.push(status)
    }

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM repairs r 
      LEFT JOIN customers c ON r.customer_id = c.id 
      ${whereClause}
    `
    const countResult = await executeQuery<any[]>(countQuery, params)
    const total = countResult[0].total

    // Get repairs with customer info
    const repairsQuery = `
      SELECT 
        r.id, r.repair_number, r.customer_id, r.item_description, r.repair_type,
        r.estimated_cost, r.actual_cost, r.advance_paid, r.status,
        r.received_date, r.promised_date, r.completed_date, r.delivered_date,
        r.notes, r.created_at, r.updated_at,
        COALESCE(c.name, r.customer_name) as customer_name,
        COALESCE(c.phone, r.customer_phone) as customer_phone,
        c.email as customer_email,
        DATEDIFF(CURDATE(), r.received_date) as days_since_received,
        CASE 
          WHEN r.promised_date IS NOT NULL THEN DATEDIFF(r.promised_date, CURDATE())
          ELSE NULL 
        END as days_to_promised
      FROM repairs r 
      LEFT JOIN customers c ON r.customer_id = c.id 
      ${whereClause}
      ORDER BY r.created_at DESC
      LIMIT ? OFFSET ?
    `
    
    const repairs = await executeQuery<any[]>(repairsQuery, [...params, limit, offset])

    return NextResponse.json({
      success: true,
      data: {
        repairs,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    })

  } catch (error) {
    console.error('Get repairs error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch repairs' },
      { status: 500 }
    )
  }
}

// POST - Create new repair
export async function POST(request: NextRequest) {
  try {
    const data = await request.json()
    
    const {
      customer_id,
      customer_name,
      customer_phone,
      item_description,
      repair_type,
      estimated_cost,
      advance_paid = 0,
      received_date,
      promised_date,
      notes
    } = data

    // Validate required fields
    if (!customer_name || !item_description || !repair_type || !received_date) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      )
    }

    const repairId = uuidv4()
    
    // Generate repair number
    const today = new Date()
    const year = today.getFullYear().toString().slice(-2)
    const month = String(today.getMonth() + 1).padStart(2, '0')
    const day = String(today.getDate()).padStart(2, '0')
    const timestamp = Date.now().toString().slice(-4)
    const repair_number = `REP-${year}${month}${day}-${timestamp}`

    await executeQuery(`
      INSERT INTO repairs (
        id, repair_number, customer_id, customer_name, customer_phone,
        item_description, repair_type, estimated_cost, advance_paid,
        status, received_date, promised_date, notes, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `, [
      repairId, repair_number, customer_id || null, customer_name, customer_phone || null,
      item_description, repair_type, estimated_cost || null, advance_paid,
      'received', received_date, promised_date || null, notes || null
    ])

    return NextResponse.json({
      success: true,
      data: { id: repairId, repair_number },
      message: 'Repair job created successfully'
    })

  } catch (error) {
    console.error('Create repair error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create repair job' },
      { status: 500 }
    )
  }
}

// PUT - Update repair
export async function PUT(request: NextRequest) {
  try {
    const data = await request.json()
    const { id, ...updateData } = data

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Repair ID is required' },
        { status: 400 }
      )
    }

    // Handle status updates with automatic date setting
    if (updateData.status) {
      const now = new Date().toISOString().split('T')[0]
      
      switch (updateData.status) {
        case 'completed':
          if (!updateData.completed_date) {
            updateData.completed_date = now
          }
          break
        case 'delivered':
          if (!updateData.delivered_date) {
            updateData.delivered_date = now
          }
          if (!updateData.completed_date) {
            updateData.completed_date = now
          }
          break
      }
    }

    // Build dynamic update query
    const updateFields = []
    const params = []

    for (const [key, value] of Object.entries(updateData)) {
      if (value !== undefined && key !== 'id') {
        updateFields.push(`${key} = ?`)
        params.push(value)
      }
    }

    if (updateFields.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No fields to update' },
        { status: 400 }
      )
    }

    updateFields.push('updated_at = NOW()')
    params.push(id)

    const query = `UPDATE repairs SET ${updateFields.join(', ')} WHERE id = ?`
    
    await executeQuery(query, params)

    return NextResponse.json({
      success: true,
      message: 'Repair updated successfully'
    })

  } catch (error) {
    console.error('Update repair error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update repair' },
      { status: 500 }
    )
  }
}

// DELETE - Delete repair
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Repair ID is required' },
        { status: 400 }
      )
    }

    // Soft delete - update status to 'cancelled'
    await executeQuery(
      'UPDATE repairs SET status = ?, updated_at = NOW() WHERE id = ?',
      ['cancelled', id]
    )

    return NextResponse.json({
      success: true,
      message: 'Repair job cancelled successfully'
    })

  } catch (error) {
    console.error('Delete repair error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to cancel repair job' },
      { status: 500 }
    )
  }
}
