import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/config/database'
import { v4 as uuidv4 } from 'uuid'

// GET - Fetch all sales
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || ''

    const offset = (page - 1) * limit

    let whereClause = 'WHERE s.status != "deleted"'
    const params: any[] = []

    if (search) {
      whereClause += ' AND (s.invoice_number LIKE ? OR c.name LIKE ? OR c.phone LIKE ?)'
      params.push(`%${search}%`, `%${search}%`, `%${search}%`)
    }

    if (status) {
      whereClause += ' AND s.status = ?'
      params.push(status)
    }

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM sales s 
      LEFT JOIN customers c ON s.customer_id = c.id 
      ${whereClause}
    `
    const countResult = await executeQuery<any[]>(countQuery, params)
    const total = countResult[0].total

    // Get sales with customer info
    const salesQuery = `
      SELECT
        s.id, s.invoice_number, s.customer_id, s.total_amount, s.subtotal,
        s.discount_amount, s.cgst_amount, s.sgst_amount, s.igst_amount,
        s.payment_method, s.payment_status,
        s.status, s.notes, s.created_at, s.updated_at,
        c.name as customer_name,
        c.phone as customer_phone,
        c.email as customer_email
      FROM sales s 
      LEFT JOIN customers c ON s.customer_id = c.id 
      ${whereClause}
      ORDER BY s.created_at DESC
      LIMIT ${limit} OFFSET ${offset}
    `

    const sales = await executeQuery<any[]>(salesQuery, params)

    // Get sale items for each sale
    for (const sale of sales) {
      const itemsQuery = `
        SELECT
          si.id, si.inventory_id, si.quantity, si.item_total, si.making_charges,
          ii.name as item_name, ii.item_code
        FROM sale_items si
        LEFT JOIN inventory ii ON si.inventory_id = ii.id
        WHERE si.sale_id = ?
      `
      const items = await executeQuery<any[]>(itemsQuery, [sale.id])
      sale.items = items
    }

    return NextResponse.json({
      success: true,
      data: {
        sales,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    })

  } catch (error) {
    console.error('Get sales error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch sales' },
      { status: 500 }
    )
  }
}

// POST - Create new sale
export async function POST(request: NextRequest) {
  try {
    const data = await request.json()
    
    const {
      customer_id,
      customer_name,
      customer_phone,
      payment_method,
      payment_status = 'pending',
      discount_amount = 0,
      tax_amount = 0,
      notes,
      items,
      total_amount,
      subtotal
    } = data

    // Validate required fields
    if (!customer_name || !payment_method || !items || items.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      )
    }

    const saleId = uuidv4()
    
    // Generate invoice number
    const today = new Date()
    const year = today.getFullYear()
    const month = String(today.getMonth() + 1).padStart(2, '0')
    const day = String(today.getDate()).padStart(2, '0')
    const timestamp = Date.now().toString().slice(-4)
    const invoice_number = `INV-${year}${month}${day}-${timestamp}`

    // Start transaction
    await executeQuery('START TRANSACTION')

    try {
      // Insert sale record
      await executeQuery(`
        INSERT INTO sales (
          id, invoice_number, customer_id, customer_name, customer_phone,
          subtotal, discount_amount, tax_amount, total_amount,
          payment_method, payment_status, status, notes, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        saleId, invoice_number, customer_id || null, customer_name, customer_phone || null,
        subtotal, discount_amount, tax_amount, total_amount,
        payment_method, payment_status, 'active', notes || null
      ])

      // Insert sale items and update inventory
      for (const item of items) {
        const itemId = uuidv4()
        
        // Insert sale item
        await executeQuery(`
          INSERT INTO sale_items (
            id, sale_id, item_id, quantity, unit_price, discount, total, created_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
        `, [
          itemId, saleId, item.item_id, item.quantity, item.unit_price, item.discount, item.total
        ])

        // Update inventory stock
        await executeQuery(`
          UPDATE inventory
          SET stock_quantity = stock_quantity - ?, updated_at = NOW()
          WHERE id = ? AND stock_quantity >= ?
        `, [item.quantity, item.item_id, item.quantity])

        // Check if stock was actually updated
        const stockCheck = await executeQuery<any[]>(
          'SELECT stock_quantity FROM inventory WHERE id = ?',
          [item.item_id]
        )
        
        if (stockCheck.length === 0) {
          throw new Error(`Item not found: ${item.item_name}`)
        }
      }

      // Commit transaction
      await executeQuery('COMMIT')

      return NextResponse.json({
        success: true,
        data: { id: saleId, invoice_number },
        message: 'Sale created successfully'
      })

    } catch (error) {
      // Rollback transaction
      await executeQuery('ROLLBACK')
      throw error
    }

  } catch (error) {
    console.error('Create sale error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create sale' },
      { status: 500 }
    )
  }
}

// PUT - Update sale
export async function PUT(request: NextRequest) {
  try {
    const data = await request.json()
    const { id, ...updateData } = data

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Sale ID is required' },
        { status: 400 }
      )
    }

    // Build dynamic update query
    const updateFields = []
    const params = []

    for (const [key, value] of Object.entries(updateData)) {
      if (value !== undefined && key !== 'id' && key !== 'items') {
        updateFields.push(`${key} = ?`)
        params.push(value)
      }
    }

    if (updateFields.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No fields to update' },
        { status: 400 }
      )
    }

    updateFields.push('updated_at = NOW()')
    params.push(id)

    const query = `UPDATE sales SET ${updateFields.join(', ')} WHERE id = ?`
    
    await executeQuery(query, params)

    return NextResponse.json({
      success: true,
      message: 'Sale updated successfully'
    })

  } catch (error) {
    console.error('Update sale error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update sale' },
      { status: 500 }
    )
  }
}

// DELETE - Delete sale
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Sale ID is required' },
        { status: 400 }
      )
    }

    // Start transaction to restore inventory and delete sale
    await executeQuery('START TRANSACTION')

    try {
      // Get sale items to restore inventory
      const saleItems = await executeQuery<any[]>(
        'SELECT item_id, quantity FROM sale_items WHERE sale_id = ?',
        [id]
      )

      // Restore inventory stock
      for (const item of saleItems) {
        await executeQuery(`
          UPDATE inventory
          SET stock_quantity = stock_quantity + ?, updated_at = NOW()
          WHERE id = ?
        `, [item.quantity, item.item_id])
      }

      // Soft delete sale
      await executeQuery(
        'UPDATE sales SET status = ?, updated_at = NOW() WHERE id = ?',
        ['deleted', id]
      )

      // Commit transaction
      await executeQuery('COMMIT')

      return NextResponse.json({
        success: true,
        message: 'Sale deleted successfully'
      })

    } catch (error) {
      // Rollback transaction
      await executeQuery('ROLLBACK')
      throw error
    }

  } catch (error) {
    console.error('Delete sale error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete sale' },
      { status: 500 }
    )
  }
}
