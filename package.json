{"name": "jewellery-pro", "version": "1.0.0", "description": "Production-ready Indian Jewellery Business Management Software", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "db:init": "tsx scripts/database/init-database.ts", "db:migrate": "tsx scripts/database/run-migrations.ts", "db:seed": "tsx scripts/database/seed-database.ts", "db:reset": "tsx scripts/database/reset-database.ts", "db:backup": "tsx scripts/database/backup-database.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "deploy:staging": "tsx scripts/deployment/deploy-staging.ts", "deploy:production": "tsx scripts/deployment/deploy-production.ts", "setup": "tsx scripts/setup.ts"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-tooltip": "^1.1.6", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^4.1.0", "dotenv": "^17.2.1", "embla-carousel-react": "^8.5.1", "framer-motion": "^11.11.17", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "lucide-react": "^0.454.0", "mysql2": "^3.6.5", "next": "^14.2.16", "next-themes": "^0.4.4", "node-fetch": "^3.3.2", "react": "^18.3.1", "react-day-picker": "^9.8.0", "react-dom": "^18.3.1", "react-hook-form": "^7.61.1", "react-hot-toast": "^2.4.1", "recharts": "^2.15.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^10.0.0", "xlsx": "^0.18.5", "zod": "^3.24.1", "zustand": "^5.0.2"}, "devDependencies": {"@playwright/test": "^1.49.1", "@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.14", "@types/node": "^22.10.2", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.20", "eslint": "^8.57.1", "eslint-config-next": "^14.2.16", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.5.0", "tailwindcss": "^3.4.17", "tsx": "^4.7.0", "typescript": "^5.7.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}