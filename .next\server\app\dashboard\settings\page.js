/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/settings/page";
exports.ids = ["app/dashboard/settings/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fsettings%2Fpage&page=%2Fdashboard%2Fsettings%2Fpage&appPaths=%2Fdashboard%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fsettings%2Fpage.tsx&appDir=C%3A%5Cproj%5Cjewellery-pro%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cjewellery-pro&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fsettings%2Fpage&page=%2Fdashboard%2Fsettings%2Fpage&appPaths=%2Fdashboard%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fsettings%2Fpage.tsx&appDir=C%3A%5Cproj%5Cjewellery-pro%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cjewellery-pro&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'settings',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/settings/page.tsx */ \"(rsc)/./app/dashboard/settings/page.tsx\")), \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/settings/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/settings/page\",\n        pathname: \"/dashboard/settings\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fsettings%2Fpage&page=%2Fdashboard%2Fsettings%2Fpage&appPaths=%2Fdashboard%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fsettings%2Fpage.tsx&appDir=C%3A%5Cproj%5Cjewellery-pro%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cjewellery-pro&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Capp%5C%5Cdashboard%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Capp%5C%5Cdashboard%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/settings/page.tsx */ \"(ssr)/./app/dashboard/settings/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNwcm9qJTVDJTVDamV3ZWxsZXJ5LXBybyU1QyU1Q2FwcCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q3NldHRpbmdzJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDhLQUFnRyIsInNvdXJjZXMiOlsid2VicGFjazovL2pld2VsbGVyeS1wcm8vPzE3MTciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxwcm9qXFxcXGpld2VsbGVyeS1wcm9cXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxzZXR0aW5nc1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Capp%5C%5Cdashboard%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Ccomponents%5C%5Cui%5C%5Csimple-toast.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Ccomponents%5C%5Cui%5C%5Csimple-toast.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(ssr)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/simple-toast.tsx */ \"(ssr)/./components/ui/simple-toast.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNwcm9qJTVDJTVDamV3ZWxsZXJ5LXBybyU1QyU1Q2NvbXBvbmVudHMlNUMlNUN0aGVtZS1wcm92aWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJUaGVtZVByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNwcm9qJTVDJTVDamV3ZWxsZXJ5LXBybyU1QyU1Q2NvbXBvbmVudHMlNUMlNUN1aSU1QyU1Q3NpbXBsZS10b2FzdC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJUb2FzdFByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNwcm9qJTVDJTVDamV3ZWxsZXJ5LXBybyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDcHJvaiU1QyU1Q2pld2VsbGVyeS1wcm8lNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEtBQStIO0FBQy9IO0FBQ0EsNEtBQWlJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vamV3ZWxsZXJ5LXByby8/NDE0MSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRoZW1lUHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxwcm9qXFxcXGpld2VsbGVyeS1wcm9cXFxcY29tcG9uZW50c1xcXFx0aGVtZS1wcm92aWRlci50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRvYXN0UHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxwcm9qXFxcXGpld2VsbGVyeS1wcm9cXFxcY29tcG9uZW50c1xcXFx1aVxcXFxzaW1wbGUtdG9hc3QudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Ccomponents%5C%5Cui%5C%5Csimple-toast.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cproj%5C%5Cjewellery-pro%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/dashboard/settings/page.tsx":
/*!*****************************************!*\
  !*** ./app/dashboard/settings/page.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SettingsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_dashboard_dashboard_layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/dashboard-layout */ \"(ssr)/./components/dashboard/dashboard-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Database_Download_Palette_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Database,Download,Palette,Shield,Upload,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Database_Download_Palette_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Database,Download,Palette,Shield,Upload,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Database_Download_Palette_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Database,Download,Palette,Shield,Upload,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Database_Download_Palette_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Database,Download,Palette,Shield,Upload,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Database_Download_Palette_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Database,Download,Palette,Shield,Upload,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Database_Download_Palette_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Database,Download,Palette,Shield,Upload,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Database_Download_Palette_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Database,Download,Palette,Shield,Upload,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Database_Download_Palette_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Database,Download,Palette,Shield,Upload,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction SettingsPage() {\n    const [currentUser, setCurrentUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check if user is logged in\n        const savedUser = localStorage.getItem(\"currentUser\");\n        if (savedUser) {\n            try {\n                const user = JSON.parse(savedUser);\n                setCurrentUser(user);\n            } catch (error) {\n                console.error(\"Error parsing saved user:\", error);\n                localStorage.removeItem(\"currentUser\");\n                router.push(\"/\");\n            }\n        } else {\n            router.push(\"/\");\n        }\n        setIsLoading(false);\n    }, [\n        router\n    ]);\n    const handleLogout = ()=>{\n        localStorage.removeItem(\"currentUser\");\n        router.push(\"/\");\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"spinner\"\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                lineNumber: 52,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this);\n    }\n    if (!currentUser) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_dashboard_layout__WEBPACK_IMPORTED_MODULE_3__.DashboardLayout, {\n        currentUser: currentUser,\n        onLogout: handleLogout,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold\",\n                            children: \"Settings\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"Manage your application settings and preferences\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Database_Download_Palette_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Profile Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Manage your personal information\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Full Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: `${currentUser.first_name} ${currentUser.last_name}`,\n                                                    className: \"w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                    readOnly: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Email\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"email\",\n                                                    value: currentUser.email,\n                                                    className: \"w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                    readOnly: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Role\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: currentUser.role.replace(\"_\", \" \").toUpperCase(),\n                                                    className: \"w-full mt-1 px-3 py-2 border rounded-lg bg-muted\",\n                                                    readOnly: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            className: \"w-full\",\n                                            children: \"Update Profile\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Database_Download_Palette_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Business Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Configure your business information\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Business Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Your Jewellery Store\",\n                                                    className: \"w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"GST Number\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"22AAAAA0000A1Z5\",\n                                                    className: \"w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Address\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    placeholder: \"Business address\",\n                                                    className: \"w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                    rows: 3\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            className: \"w-full\",\n                                            children: \"Update Business Info\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Database_Download_Palette_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Notifications\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Configure your notification preferences\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Low Stock Alerts\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: \"Get notified when items are low in stock\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    className: \"toggle\",\n                                                    defaultChecked: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Payment Reminders\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: \"Remind customers about pending payments\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    className: \"toggle\",\n                                                    defaultChecked: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Daily Reports\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 177,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: \"Receive daily sales and inventory reports\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    className: \"toggle\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            className: \"w-full\",\n                                            children: \"Save Preferences\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Database_Download_Palette_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Security\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Manage your account security\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outline\",\n                                            className: \"w-full\",\n                                            children: \"Change Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outline\",\n                                            className: \"w-full\",\n                                            children: \"Enable Two-Factor Authentication\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outline\",\n                                            className: \"w-full\",\n                                            children: \"View Login History\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"destructive\",\n                                            className: \"w-full\",\n                                            children: \"Deactivate Account\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Database_Download_Palette_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Appearance\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Customize the look and feel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Theme\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    className: \"w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            children: \"Light\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            children: \"Dark\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            children: \"System\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Language\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    className: \"w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            children: \"English\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            children: \"Hindi\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            children: \"Gujarati\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            className: \"w-full\",\n                                            children: \"Apply Changes\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Database_Download_Palette_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Data Management\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Backup and restore your data\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outline\",\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Database_Download_Palette_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Export Data\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outline\",\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Database_Download_Palette_Shield_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Import Data\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outline\",\n                                            className: \"w-full\",\n                                            children: \"Create Backup\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"destructive\",\n                                            className: \"w-full\",\n                                            children: \"Reset All Data\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"border-dashed\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                    children: \"Advanced Settings Coming Soon\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                    children: \"Advanced configuration options including tax settings, custom fields, integrations, and more will be available in the full version.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/dashboard/settings/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/dashboard-layout.tsx":
/*!***************************************************!*\
  !*** ./components/dashboard/dashboard-layout.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardLayout: () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./sidebar */ \"(ssr)/./components/dashboard/sidebar.tsx\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_LogOut_Menu_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,LogOut,Menu!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_LogOut_Menu_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,LogOut,Menu!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_LogOut_Menu_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,LogOut,Menu!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ DashboardLayout auto */ \n\n\n\n\nfunction DashboardLayout({ children, currentUser, onLogout }) {\n    const [isMobileSidebarOpen, setIsMobileSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sidebar__WEBPACK_IMPORTED_MODULE_3__.MobileSidebar, {\n                isOpen: isMobileSidebarOpen,\n                onClose: ()=>setIsMobileSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden lg:block\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sidebar__WEBPACK_IMPORTED_MODULE_3__.Sidebar, {}, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                                className: \"border-b bg-card px-4 py-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    className: \"lg:hidden\",\n                                                    onClick: ()=>setIsMobileSidebarOpen(true),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_LogOut_Menu_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                                        lineNumber: 49,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                                    lineNumber: 43,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 lg:hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"gold-gradient w-8 h-8 rounded-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_LogOut_Menu_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                className: \"w-4 h-4 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                                                lineNumber: 55,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                                            lineNumber: 54,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-lg font-semibold\",\n                                                            children: \"Jewellery Pro\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                                            lineNumber: 57,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                                    lineNumber: 53,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                            lineNumber: 41,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right hidden sm:block\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: [\n                                                                currentUser.first_name,\n                                                                \" \",\n                                                                currentUser.last_name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                                            lineNumber: 64,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-muted-foreground capitalize\",\n                                                            children: currentUser.role.replace(\"_\", \" \")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                                            lineNumber: 67,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: onLogout,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_LogOut_Menu_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                                            lineNumber: 72,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"hidden sm:inline\",\n                                                            children: \"Logout\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                                            lineNumber: 73,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                className: \"flex-1 overflow-auto p-6\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\dashboard-layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL2Rhc2hib2FyZC9kYXNoYm9hcmQtbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUVnQztBQUVlO0FBQ0c7QUFLN0I7QUFRZCxTQUFTTyxnQkFBZ0IsRUFBRUMsUUFBUSxFQUFFQyxXQUFXLEVBQUVDLFFBQVEsRUFBd0I7SUFDdkYsTUFBTSxDQUFDQyxxQkFBcUJDLHVCQUF1QixHQUFHWiwrQ0FBUUEsQ0FBQztJQUUvRCxxQkFDRSw4REFBQ2E7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNYLG1EQUFhQTtnQkFDWlksUUFBUUo7Z0JBQ1JLLFNBQVMsSUFBTUosdUJBQXVCOzs7Ozs7MEJBR3hDLDhEQUFDQztnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDWiw2Q0FBT0E7Ozs7Ozs7Ozs7a0NBSVYsOERBQUNXO3dCQUFJQyxXQUFVOzswQ0FFYiw4REFBQ0c7Z0NBQU9ILFdBQVU7MENBQ2hCLDRFQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFFYiw4REFBQ2IseURBQU1BO29EQUNMaUIsU0FBUTtvREFDUkMsTUFBSztvREFDTEwsV0FBVTtvREFDVk0sU0FBUyxJQUFNUix1QkFBdUI7OERBRXRDLDRFQUFDUixrR0FBSUE7d0RBQUNVLFdBQVU7Ozs7Ozs7Ozs7OzhEQUlsQiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTtzRUFDYiw0RUFBQ1Isa0dBQVVBO2dFQUFDUSxXQUFVOzs7Ozs7Ozs7OztzRUFFeEIsOERBQUNPOzREQUFHUCxXQUFVO3NFQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUsxQyw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNROzREQUFFUixXQUFVOztnRUFDVkwsWUFBWWMsVUFBVTtnRUFBQztnRUFBRWQsWUFBWWUsU0FBUzs7Ozs7OztzRUFFakQsOERBQUNGOzREQUFFUixXQUFVO3NFQUNWTCxZQUFZZ0IsSUFBSSxDQUFDQyxPQUFPLENBQUMsS0FBSzs7Ozs7Ozs7Ozs7OzhEQUduQyw4REFBQ3pCLHlEQUFNQTtvREFBQ2lCLFNBQVE7b0RBQVVDLE1BQUs7b0RBQUtDLFNBQVNWOztzRUFDM0MsOERBQUNMLGtHQUFNQTs0REFBQ1MsV0FBVTs7Ozs7O3NFQUNsQiw4REFBQ2E7NERBQUtiLFdBQVU7c0VBQW1COzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FPM0MsOERBQUNjO2dDQUFLZCxXQUFVOzBDQUNiTjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTWIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9qZXdlbGxlcnktcHJvLy4vY29tcG9uZW50cy9kYXNoYm9hcmQvZGFzaGJvYXJkLWxheW91dC50c3g/NmE3YyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IFVzZXIgfSBmcm9tICdAL2xpYi90eXBlcydcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9idXR0b24nXG5pbXBvcnQgeyBTaWRlYmFyLCBNb2JpbGVTaWRlYmFyIH0gZnJvbSAnLi9zaWRlYmFyJ1xuaW1wb3J0IHsgXG4gIE1lbnUsXG4gIExvZ091dCxcbiAgRG9sbGFyU2lnblxufSBmcm9tICdsdWNpZGUtcmVhY3QnXG5cbmludGVyZmFjZSBEYXNoYm9hcmRMYXlvdXRQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbiAgY3VycmVudFVzZXI6IFVzZXJcbiAgb25Mb2dvdXQ6ICgpID0+IHZvaWRcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIERhc2hib2FyZExheW91dCh7IGNoaWxkcmVuLCBjdXJyZW50VXNlciwgb25Mb2dvdXQgfTogRGFzaGJvYXJkTGF5b3V0UHJvcHMpIHtcbiAgY29uc3QgW2lzTW9iaWxlU2lkZWJhck9wZW4sIHNldElzTW9iaWxlU2lkZWJhck9wZW5dID0gdXNlU3RhdGUoZmFsc2UpXG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1iYWNrZ3JvdW5kXCI+XG4gICAgICB7LyogTW9iaWxlIHNpZGViYXIgKi99XG4gICAgICA8TW9iaWxlU2lkZWJhciBcbiAgICAgICAgaXNPcGVuPXtpc01vYmlsZVNpZGViYXJPcGVufSBcbiAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0SXNNb2JpbGVTaWRlYmFyT3BlbihmYWxzZSl9IFxuICAgICAgLz5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGgtc2NyZWVuXCI+XG4gICAgICAgIHsvKiBEZXNrdG9wIHNpZGViYXIgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIGxnOmJsb2NrXCI+XG4gICAgICAgICAgPFNpZGViYXIgLz5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIE1haW4gY29udGVudCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBmbGV4LWNvbCBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICAgIDxoZWFkZXIgY2xhc3NOYW1lPVwiYm9yZGVyLWIgYmctY2FyZCBweC00IHB5LTNcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgICAgey8qIE1vYmlsZSBtZW51IGJ1dHRvbiAqL31cbiAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImxnOmhpZGRlblwiXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc01vYmlsZVNpZGViYXJPcGVuKHRydWUpfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxNZW51IGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuXG4gICAgICAgICAgICAgICAgey8qIE1vYmlsZSBsb2dvICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIGxnOmhpZGRlblwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJnb2xkLWdyYWRpZW50IHctOCBoLTggcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIDxEb2xsYXJTaWduIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGRcIj5KZXdlbGxlcnkgUHJvPC9oMT5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIFVzZXIgaW5mbyBhbmQgbG9nb3V0ICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodCBoaWRkZW4gc206YmxvY2tcIj5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAge2N1cnJlbnRVc2VyLmZpcnN0X25hbWV9IHtjdXJyZW50VXNlci5sYXN0X25hbWV9XG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBjYXBpdGFsaXplXCI+XG4gICAgICAgICAgICAgICAgICAgIHtjdXJyZW50VXNlci5yb2xlLnJlcGxhY2UoJ18nLCAnICcpfVxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cIm91dGxpbmVcIiBzaXplPVwic21cIiBvbkNsaWNrPXtvbkxvZ291dH0+XG4gICAgICAgICAgICAgICAgICA8TG9nT3V0IGNsYXNzTmFtZT1cInctNCBoLTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJoaWRkZW4gc206aW5saW5lXCI+TG9nb3V0PC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvaGVhZGVyPlxuXG4gICAgICAgICAgey8qIFBhZ2UgY29udGVudCAqL31cbiAgICAgICAgICA8bWFpbiBjbGFzc05hbWU9XCJmbGV4LTEgb3ZlcmZsb3ctYXV0byBwLTZcIj5cbiAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICA8L21haW4+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsIkJ1dHRvbiIsIlNpZGViYXIiLCJNb2JpbGVTaWRlYmFyIiwiTWVudSIsIkxvZ091dCIsIkRvbGxhclNpZ24iLCJEYXNoYm9hcmRMYXlvdXQiLCJjaGlsZHJlbiIsImN1cnJlbnRVc2VyIiwib25Mb2dvdXQiLCJpc01vYmlsZVNpZGViYXJPcGVuIiwic2V0SXNNb2JpbGVTaWRlYmFyT3BlbiIsImRpdiIsImNsYXNzTmFtZSIsImlzT3BlbiIsIm9uQ2xvc2UiLCJoZWFkZXIiLCJ2YXJpYW50Iiwic2l6ZSIsIm9uQ2xpY2siLCJoMSIsInAiLCJmaXJzdF9uYW1lIiwibGFzdF9uYW1lIiwicm9sZSIsInJlcGxhY2UiLCJzcGFuIiwibWFpbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/dashboard-layout.tsx\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/sidebar.tsx":
/*!******************************************!*\
  !*** ./components/dashboard/sidebar.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MobileSidebar: () => (/* binding */ MobileSidebar),\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DollarSign_FileText_Home_Menu_Package_Settings_ShoppingCart_TrendingUp_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DollarSign,FileText,Home,Menu,Package,Settings,ShoppingCart,TrendingUp,Users,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DollarSign_FileText_Home_Menu_Package_Settings_ShoppingCart_TrendingUp_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DollarSign,FileText,Home,Menu,Package,Settings,ShoppingCart,TrendingUp,Users,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DollarSign_FileText_Home_Menu_Package_Settings_ShoppingCart_TrendingUp_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DollarSign,FileText,Home,Menu,Package,Settings,ShoppingCart,TrendingUp,Users,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DollarSign_FileText_Home_Menu_Package_Settings_ShoppingCart_TrendingUp_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DollarSign,FileText,Home,Menu,Package,Settings,ShoppingCart,TrendingUp,Users,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DollarSign_FileText_Home_Menu_Package_Settings_ShoppingCart_TrendingUp_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DollarSign,FileText,Home,Menu,Package,Settings,ShoppingCart,TrendingUp,Users,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DollarSign_FileText_Home_Menu_Package_Settings_ShoppingCart_TrendingUp_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DollarSign,FileText,Home,Menu,Package,Settings,ShoppingCart,TrendingUp,Users,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DollarSign_FileText_Home_Menu_Package_Settings_ShoppingCart_TrendingUp_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DollarSign,FileText,Home,Menu,Package,Settings,ShoppingCart,TrendingUp,Users,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DollarSign_FileText_Home_Menu_Package_Settings_ShoppingCart_TrendingUp_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DollarSign,FileText,Home,Menu,Package,Settings,ShoppingCart,TrendingUp,Users,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DollarSign_FileText_Home_Menu_Package_Settings_ShoppingCart_TrendingUp_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DollarSign,FileText,Home,Menu,Package,Settings,ShoppingCart,TrendingUp,Users,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DollarSign_FileText_Home_Menu_Package_Settings_ShoppingCart_TrendingUp_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DollarSign,FileText,Home,Menu,Package,Settings,ShoppingCart,TrendingUp,Users,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DollarSign_FileText_Home_Menu_Package_Settings_ShoppingCart_TrendingUp_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DollarSign,FileText,Home,Menu,Package,Settings,ShoppingCart,TrendingUp,Users,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DollarSign_FileText_Home_Menu_Package_Settings_ShoppingCart_TrendingUp_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DollarSign,FileText,Home,Menu,Package,Settings,ShoppingCart,TrendingUp,Users,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ Sidebar,MobileSidebar auto */ \n\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Dashboard\",\n        href: \"/dashboard\",\n        icon: _barrel_optimize_names_CreditCard_DollarSign_FileText_Home_Menu_Package_Settings_ShoppingCart_TrendingUp_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: \"Inventory\",\n        href: \"/dashboard/inventory\",\n        icon: _barrel_optimize_names_CreditCard_DollarSign_FileText_Home_Menu_Package_Settings_ShoppingCart_TrendingUp_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: \"Sales\",\n        href: \"/dashboard/sales\",\n        icon: _barrel_optimize_names_CreditCard_DollarSign_FileText_Home_Menu_Package_Settings_ShoppingCart_TrendingUp_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: \"Customers\",\n        href: \"/dashboard/customers\",\n        icon: _barrel_optimize_names_CreditCard_DollarSign_FileText_Home_Menu_Package_Settings_ShoppingCart_TrendingUp_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        name: \"Purchases\",\n        href: \"/dashboard/purchases\",\n        icon: _barrel_optimize_names_CreditCard_DollarSign_FileText_Home_Menu_Package_Settings_ShoppingCart_TrendingUp_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        name: \"Schemes\",\n        href: \"/dashboard/schemes\",\n        icon: _barrel_optimize_names_CreditCard_DollarSign_FileText_Home_Menu_Package_Settings_ShoppingCart_TrendingUp_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        name: \"Repairs\",\n        href: \"/dashboard/repairs\",\n        icon: _barrel_optimize_names_CreditCard_DollarSign_FileText_Home_Menu_Package_Settings_ShoppingCart_TrendingUp_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    },\n    {\n        name: \"Reports\",\n        href: \"/dashboard/reports\",\n        icon: _barrel_optimize_names_CreditCard_DollarSign_FileText_Home_Menu_Package_Settings_ShoppingCart_TrendingUp_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n    },\n    {\n        name: \"Settings\",\n        href: \"/dashboard/settings\",\n        icon: _barrel_optimize_names_CreditCard_DollarSign_FileText_Home_Menu_Package_Settings_ShoppingCart_TrendingUp_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    }\n];\nfunction Sidebar({ className }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex flex-col h-full bg-card border-r transition-all duration-300\", isCollapsed ? \"w-16\" : \"w-64\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-4 border-b\",\n                children: [\n                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"gold-gradient w-8 h-8 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_DollarSign_FileText_Home_Menu_Package_Settings_ShoppingCart_TrendingUp_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"w-4 h-4 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"Jewellery Pro\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        onClick: ()=>setIsCollapsed(!isCollapsed),\n                        className: \"h-8 w-8 p-0\",\n                        children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_DollarSign_FileText_Home_Menu_Package_Settings_ShoppingCart_TrendingUp_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 26\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_DollarSign_FileText_Home_Menu_Package_Settings_ShoppingCart_TrendingUp_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 57\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 p-4 space-y-2\",\n                children: navigation.map((item)=>{\n                    const isActive = pathname === item.href;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        href: item.href,\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors\", isActive ? \"bg-primary text-primary-foreground\" : \"text-muted-foreground hover:text-foreground hover:bg-muted\", isCollapsed && \"justify-center\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                className: \"h-5 w-5 flex-shrink-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 15\n                            }, this),\n                            !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: item.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 32\n                            }, this)\n                        ]\n                    }, item.name, true, {\n                        fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t\",\n                children: !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-muted-foreground\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Version 1.0.0\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"\\xa9 2024 Jewellery Pro\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\sidebar.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\nfunction MobileSidebar({ isOpen, onClose }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 z-40 lg:hidden\",\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-y-0 left-0 z-50 w-64 bg-card border-r lg:hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4 border-b\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"gold-gradient w-8 h-8 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_DollarSign_FileText_Home_Menu_Package_Settings_ShoppingCart_TrendingUp_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-4 h-4 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: \"Jewellery Pro\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: onClose,\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_DollarSign_FileText_Home_Menu_Package_Settings_ShoppingCart_TrendingUp_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex-1 p-4 space-y-2\",\n                        children: navigation.map((item)=>{\n                            const isActive = pathname === item.href;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: item.href,\n                                onClick: onClose,\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors\", isActive ? \"bg-primary text-primary-foreground\" : \"text-muted-foreground hover:text-foreground hover:bg-muted\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, item.name, true, {\n                                fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThCO0FBQ21DO0FBRzFELFNBQVNDLGNBQWMsRUFBRUUsUUFBUSxFQUFFLEdBQUdDLE9BQTJCO0lBQ3RFLHFCQUFPLDhEQUFDRixzREFBa0JBO1FBQUUsR0FBR0UsS0FBSztrQkFBR0Q7Ozs7OztBQUN6QyIsInNvdXJjZXMiOlsid2VicGFjazovL2pld2VsbGVyeS1wcm8vLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeD85Mjg5Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCdcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgYXMgTmV4dFRoZW1lc1Byb3ZpZGVyIH0gZnJvbSAnbmV4dC10aGVtZXMnXG5pbXBvcnQgdHlwZSB7IFRoZW1lUHJvdmlkZXJQcm9wcyB9IGZyb20gJ25leHQtdGhlbWVzJ1xuXG5leHBvcnQgZnVuY3Rpb24gVGhlbWVQcm92aWRlcih7IGNoaWxkcmVuLCAuLi5wcm9wcyB9OiBUaGVtZVByb3ZpZGVyUHJvcHMpIHtcbiAgcmV0dXJuIDxOZXh0VGhlbWVzUHJvdmlkZXIgey4uLnByb3BzfT57Y2hpbGRyZW59PC9OZXh0VGhlbWVzUHJvdmlkZXI+XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJUaGVtZVByb3ZpZGVyIiwiTmV4dFRoZW1lc1Byb3ZpZGVyIiwiY2hpbGRyZW4iLCJwcm9wcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/simple-toast.tsx":
/*!****************************************!*\
  !*** ./components/ui/simple-toast.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ToastProvider,useToast auto */ \n\nconst ToastContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ToastProvider({ children }) {\n    const [toasts, setToasts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const toast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((newToast)=>{\n        const id = Math.random().toString(36).substr(2, 9);\n        const toastWithId = {\n            ...newToast,\n            id\n        };\n        setToasts((prev)=>[\n                ...prev,\n                toastWithId\n            ]);\n        // Auto remove after 5 seconds\n        setTimeout(()=>{\n            setToasts((prev)=>prev.filter((t)=>t.id !== id));\n        }, 5000);\n    }, []);\n    const removeToast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((id)=>{\n        setToasts((prev)=>prev.filter((t)=>t.id !== id));\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContext.Provider, {\n        value: {\n            toasts,\n            toast,\n            removeToast\n        },\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\ui\\\\simple-toast.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\ui\\\\simple-toast.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\nfunction useToast() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ToastContext);\n    if (!context) {\n        throw new Error(\"useToast must be used within a ToastProvider\");\n    }\n    return context;\n}\nfunction ToastContainer() {\n    const { toasts, removeToast } = useToast();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 z-50 space-y-2\",\n        children: toasts.map((toast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `\n            max-w-sm p-4 rounded-lg shadow-lg border\n            ${toast.variant === \"destructive\" ? \"bg-red-50 border-red-200 text-red-800\" : \"bg-white border-gray-200 text-gray-900\"}\n            animate-in slide-in-from-top-2 duration-300\n          `,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-sm\",\n                                    children: toast.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\ui\\\\simple-toast.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, this),\n                                toast.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm opacity-90 mt-1\",\n                                    children: toast.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\ui\\\\simple-toast.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\ui\\\\simple-toast.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>removeToast(toast.id),\n                            className: \"ml-2 text-gray-400 hover:text-gray-600\",\n                            children: \"\\xd7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\ui\\\\simple-toast.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\ui\\\\simple-toast.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 11\n                }, this)\n            }, toast.id, false, {\n                fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\ui\\\\simple-toast.tsx\",\n                lineNumber: 61,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\jewellery-pro\\\\components\\\\ui\\\\simple-toast.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/simple-toast.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateGST: () => (/* binding */ calculateGST),\n/* harmony export */   calculatePercentage: () => (/* binding */ calculatePercentage),\n/* harmony export */   calculateTotalWithGST: () => (/* binding */ calculateTotalWithGST),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   deepClone: () => (/* binding */ deepClone),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatWeight: () => (/* binding */ formatWeight),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   generateSlug: () => (/* binding */ generateSlug),\n/* harmony export */   getFileExtension: () => (/* binding */ getFileExtension),\n/* harmony export */   isEmpty: () => (/* binding */ isEmpty),\n/* harmony export */   isValidAadhar: () => (/* binding */ isValidAadhar),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   isValidGST: () => (/* binding */ isValidGST),\n/* harmony export */   isValidPAN: () => (/* binding */ isValidPAN),\n/* harmony export */   isValidPhone: () => (/* binding */ isValidPhone),\n/* harmony export */   roundTo: () => (/* binding */ roundTo),\n/* harmony export */   stringToColor: () => (/* binding */ stringToColor),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   toTitleCase: () => (/* binding */ toTitleCase)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n// Format currency\nfunction formatCurrency(amount, currency = \"INR\") {\n    return new Intl.NumberFormat(\"en-IN\", {\n        style: \"currency\",\n        currency: currency,\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n    }).format(amount);\n}\n// Format number with Indian numbering system\nfunction formatNumber(num) {\n    return new Intl.NumberFormat(\"en-IN\").format(num);\n}\n// Format weight (grams)\nfunction formatWeight(weight) {\n    return `${weight.toFixed(3)}g`;\n}\n// Format date\nfunction formatDate(date, format = \"short\") {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    switch(format){\n        case \"long\":\n            return dateObj.toLocaleDateString(\"en-IN\", {\n                year: \"numeric\",\n                month: \"long\",\n                day: \"numeric\"\n            });\n        case \"datetime\":\n            return dateObj.toLocaleString(\"en-IN\", {\n                year: \"numeric\",\n                month: \"short\",\n                day: \"numeric\",\n                hour: \"2-digit\",\n                minute: \"2-digit\"\n            });\n        default:\n            return dateObj.toLocaleDateString(\"en-IN\");\n    }\n}\n// Calculate GST\nfunction calculateGST(amount, rate) {\n    return amount * rate / 100;\n}\n// Calculate total with GST\nfunction calculateTotalWithGST(subtotal, cgstRate = 1.5, sgstRate = 1.5, igstRate = 0) {\n    const cgstAmount = igstRate > 0 ? 0 : calculateGST(subtotal, cgstRate);\n    const sgstAmount = igstRate > 0 ? 0 : calculateGST(subtotal, sgstRate);\n    const igstAmount = igstRate > 0 ? calculateGST(subtotal, igstRate) : 0;\n    const totalAmount = subtotal + cgstAmount + sgstAmount + igstAmount;\n    return {\n        cgstAmount,\n        sgstAmount,\n        igstAmount,\n        totalAmount\n    };\n}\n// Generate random ID\nfunction generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\n// Validate email\nfunction isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n// Validate phone number (Indian format)\nfunction isValidPhone(phone) {\n    const phoneRegex = /^[+]?[91]?[6-9]\\d{9}$/;\n    return phoneRegex.test(phone.replace(/\\s+/g, \"\"));\n}\n// Validate GST number\nfunction isValidGST(gst) {\n    const gstRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;\n    return gstRegex.test(gst);\n}\n// Validate PAN number\nfunction isValidPAN(pan) {\n    const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;\n    return panRegex.test(pan);\n}\n// Validate Aadhar number\nfunction isValidAadhar(aadhar) {\n    const aadharRegex = /^[2-9]{1}[0-9]{3}[0-9]{4}[0-9]{4}$/;\n    return aadharRegex.test(aadhar.replace(/\\s+/g, \"\"));\n}\n// Debounce function\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n// Throttle function\nfunction throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n// Deep clone object\nfunction deepClone(obj) {\n    if (obj === null || typeof obj !== \"object\") return obj;\n    if (obj instanceof Date) return new Date(obj.getTime());\n    if (obj instanceof Array) return obj.map((item)=>deepClone(item));\n    if (typeof obj === \"object\") {\n        const clonedObj = {};\n        for(const key in obj){\n            if (obj.hasOwnProperty(key)) {\n                clonedObj[key] = deepClone(obj[key]);\n            }\n        }\n        return clonedObj;\n    }\n    return obj;\n}\n// Convert string to title case\nfunction toTitleCase(str) {\n    return str.replace(/\\w\\S*/g, (txt)=>txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());\n}\n// Generate slug from string\nfunction generateSlug(str) {\n    return str.toLowerCase().replace(/[^\\w ]+/g, \"\").replace(/ +/g, \"-\");\n}\n// Calculate percentage\nfunction calculatePercentage(value, total) {\n    if (total === 0) return 0;\n    return value / total * 100;\n}\n// Round to decimal places\nfunction roundTo(num, decimals = 2) {\n    return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);\n}\n// Check if object is empty\nfunction isEmpty(obj) {\n    if (obj == null) return true;\n    if (Array.isArray(obj) || typeof obj === \"string\") return obj.length === 0;\n    if (typeof obj === \"object\") return Object.keys(obj).length === 0;\n    return false;\n}\n// Get file extension\nfunction getFileExtension(filename) {\n    return filename.slice((filename.lastIndexOf(\".\") - 1 >>> 0) + 2);\n}\n// Format file size\nfunction formatFileSize(bytes) {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\n        \"Bytes\",\n        \"KB\",\n        \"MB\",\n        \"GB\"\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n}\n// Generate color from string\nfunction stringToColor(str) {\n    let hash = 0;\n    for(let i = 0; i < str.length; i++){\n        hash = str.charCodeAt(i) + ((hash << 5) - hash);\n    }\n    const color = Math.abs(hash).toString(16).substring(0, 6);\n    return \"#\" + \"000000\".substring(0, 6 - color.length) + color;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9abb80a80afd\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9qZXdlbGxlcnktcHJvLy4vYXBwL2dsb2JhbHMuY3NzP2M5YzkiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5YWJiODBhODBhZmRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/dashboard/settings/page.tsx":
/*!*****************************************!*\
  !*** ./app/dashboard/settings/page.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\proj\jewellery-pro\app\dashboard\settings\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./components/theme-provider.tsx\");\n/* harmony import */ var _components_ui_simple_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/simple-toast */ \"(rsc)/./components/ui/simple-toast.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"Jewellery Pro - Business Management System\",\n    description: \"Complete Indian Jewellery Business Management Software\",\n    keywords: \"jewellery, jewelry, business, management, inventory, sales, gold, silver\",\n    authors: [\n        {\n            name: \"Jewellery Pro Team\"\n        }\n    ]\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"light\",\n                enableSystem: true,\n                disableTransitionOnChange: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_simple_toast__WEBPACK_IMPORTED_MODULE_3__.ToastProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\layout.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\layout.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\proj\jewellery-pro\components\theme-provider.tsx#ThemeProvider`);


/***/ }),

/***/ "(rsc)/./components/ui/simple-toast.tsx":
/*!****************************************!*\
  !*** ./components/ui/simple-toast.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ToastProvider: () => (/* binding */ e0),
/* harmony export */   useToast: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\proj\jewellery-pro\components\ui\simple-toast.tsx#ToastProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\proj\jewellery-pro\components\ui\simple-toast.tsx#useToast`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/next-themes","vendor-chunks/@radix-ui","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fsettings%2Fpage&page=%2Fdashboard%2Fsettings%2Fpage&appPaths=%2Fdashboard%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fsettings%2Fpage.tsx&appDir=C%3A%5Cproj%5Cjewellery-pro%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cjewellery-pro&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();