import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/config/database'
import { v4 as uuidv4 } from 'uuid'

// GET - Fetch all inventory items
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const category = searchParams.get('category') || ''
    const status = searchParams.get('status') || ''

    const offset = (page - 1) * limit

    let whereClause = 'WHERE 1=1'
    const params: any[] = []

    if (search) {
      whereClause += ' AND (name LIKE ? OR item_code LIKE ? OR description LIKE ?)'
      params.push(`%${search}%`, `%${search}%`, `%${search}%`)
    }

    if (category) {
      whereClause += ' AND category_id = ?'
      params.push(category)
    }

    if (status) {
      whereClause += ' AND status = ?'
      params.push(status)
    }

    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM inventory ${whereClause}`
    const countResult = await executeQuery<any[]>(countQuery, params)
    const total = countResult[0].total

    // Get items with pagination - simplified query first
    const itemsQuery = `
      SELECT
        id, item_code, name, metal_type, purity, gross_weight as weight,
        making_charges, stone_amount as stone_charges, other_charges,
        total_amount as total_value, stock_quantity as current_stock,
        min_stock_level, supplier_id, description, status, created_at, updated_at
      FROM inventory
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT ${limit} OFFSET ${offset}
    `

    const items = await executeQuery<any[]>(itemsQuery, params)

    return NextResponse.json({
      success: true,
      data: {
        items,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    })

  } catch (error) {
    console.error('Get inventory items error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch inventory items' },
      { status: 500 }
    )
  }
}

// POST - Create new inventory item
export async function POST(request: NextRequest) {
  try {
    const data = await request.json()
    
    const {
      name,
      category,
      subcategory,
      metal_type,
      purity,
      weight,
      making_charges = 0,
      stone_charges = 0,
      other_charges = 0,
      description,
      supplier_id,
      min_stock_level = 1,
      location,
      barcode
    } = data

    // Validate required fields
    if (!name || !category || !metal_type || !purity || !weight) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      )
    }

    const id = uuidv4()
    
    // Generate item code
    const codePrefix = category.substring(0, 3).toUpperCase()
    const timestamp = Date.now().toString().slice(-6)
    const item_code = `${codePrefix}-${timestamp}`

    // Calculate total value (this would typically include current metal rates)
    const total_value = parseFloat(making_charges) + parseFloat(stone_charges) + parseFloat(other_charges)

    await executeQuery(`
      INSERT INTO inventory (
        id, item_code, name, metal_type, purity, gross_weight,
        making_charges, stone_amount, other_charges, total_amount,
        stock_quantity, min_stock_level, supplier_id, description,
        barcode, status, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `, [
      id, item_code, name, metal_type, purity, weight,
      making_charges, stone_charges, other_charges, total_value,
      1, min_stock_level, supplier_id || null, description || null,
      barcode || null, 'available'
    ])

    return NextResponse.json({
      success: true,
      data: { id, item_code },
      message: 'Item added successfully'
    })

  } catch (error) {
    console.error('Create inventory item error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create inventory item' },
      { status: 500 }
    )
  }
}

// PUT - Update inventory item
export async function PUT(request: NextRequest) {
  try {
    const data = await request.json()
    const { id, ...updateData } = data

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Item ID is required' },
        { status: 400 }
      )
    }

    // Build dynamic update query
    const updateFields = []
    const params = []

    for (const [key, value] of Object.entries(updateData)) {
      if (value !== undefined && key !== 'id') {
        updateFields.push(`${key} = ?`)
        params.push(value)
      }
    }

    if (updateFields.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No fields to update' },
        { status: 400 }
      )
    }

    updateFields.push('updated_at = NOW()')
    params.push(id)

    const query = `UPDATE inventory SET ${updateFields.join(', ')} WHERE id = ?`
    
    await executeQuery(query, params)

    return NextResponse.json({
      success: true,
      message: 'Item updated successfully'
    })

  } catch (error) {
    console.error('Update inventory item error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update inventory item' },
      { status: 500 }
    )
  }
}

// DELETE - Delete inventory item
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Item ID is required' },
        { status: 400 }
      )
    }

    // Soft delete - update status to 'deleted'
    await executeQuery(
      'UPDATE inventory SET status = ?, updated_at = NOW() WHERE id = ?',
      ['deleted', id]
    )

    return NextResponse.json({
      success: true,
      message: 'Item deleted successfully'
    })

  } catch (error) {
    console.error('Delete inventory item error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete inventory item' },
      { status: 500 }
    )
  }
}
