import { executeQuery, executeDDLQuery } from '../../config/database'
import { readFileSync } from 'fs'
import { join } from 'path'

function parseSchemaStatements(schema: string): string[] {
  const statements: string[] = []
  let currentDelimiter = ';'
  let currentStatement = ''

  const lines = schema.split('\n')

  for (const line of lines) {
    const trimmedLine = line.trim()

    // Skip empty lines and comments
    if (!trimmedLine || trimmedLine.startsWith('--')) {
      continue
    }

    // Skip database creation and use statements
    if (trimmedLine.toUpperCase().startsWith('CREATE DATABASE') ||
        trimmedLine.toUpperCase().startsWith('USE ')) {
      continue
    }

    // Handle delimiter changes
    if (trimmedLine.toUpperCase().startsWith('DELIMITER ')) {
      currentDelimiter = trimmedLine.substring(10).trim()
      continue
    }

    // Add line to current statement
    currentStatement += (currentStatement ? '\n' : '') + line

    // Check if statement is complete
    if (trimmedLine.endsWith(currentDelimiter)) {
      // Remove the delimiter from the end
      const statement = currentStatement.substring(0, currentStatement.lastIndexOf(currentDelimiter)).trim()
      if (statement) {
        statements.push(statement)
      }
      currentStatement = ''
    }
  }

  // Add any remaining statement
  if (currentStatement.trim()) {
    statements.push(currentStatement.trim())
  }

  return statements
}

export const migration = {
  id: '001_initial_schema',
  description: 'Create initial database schema with all tables',
  
  async up(): Promise<void> {
    console.log('Running migration: 001_initial_schema')
    
    try {
      // Read the schema file
      const schemaPath = join(process.cwd(), 'lib', 'database', 'schema.sql')
      const schema = readFileSync(schemaPath, 'utf8')
      
      // Parse the schema handling different delimiters
      const statements = parseSchemaStatements(schema)
      
      // Execute each statement
      for (const statement of statements) {
        if (statement.trim()) {
          // Use DDL query for triggers and other DDL statements that don't support prepared statements
          if (statement.toUpperCase().includes('CREATE TRIGGER') ||
              statement.toUpperCase().includes('CREATE PROCEDURE') ||
              statement.toUpperCase().includes('CREATE FUNCTION')) {
            await executeDDLQuery(statement)
          } else {
            await executeQuery(statement)
          }
        }
      }
      
      console.log('✅ Initial schema created successfully')
    } catch (error) {
      console.error('❌ Error creating initial schema:', error)
      throw error
    }
  },
  
  async down(): Promise<void> {
    console.log('Rolling back migration: 001_initial_schema')
    
    const tables = [
      'v_customer_summary',
      'v_inventory_summary', 
      'v_sales_summary',
      'reminders',
      'notifications',
      'purchase_items',
      'purchases',
      'suppliers',
      'repairs',
      'scheme_payments',
      'schemes',
      'exchange_items',
      'exchange_transactions',
      'sale_items',
      'sales',
      'inventory',
      'inventory_categories',
      'customers',
      'audit_log',
      'metal_rates',
      'business_settings',
      'users'
    ]
    
    try {
      // Drop views first
      await executeQuery('DROP VIEW IF EXISTS v_customer_summary')
      await executeQuery('DROP VIEW IF EXISTS v_inventory_summary')
      await executeQuery('DROP VIEW IF EXISTS v_sales_summary')
      
      // Drop triggers
      await executeQuery('DROP TRIGGER IF EXISTS tr_update_customer_purchases')
      await executeQuery('DROP TRIGGER IF EXISTS tr_update_inventory_on_sale')
      await executeQuery('DROP TRIGGER IF EXISTS tr_audit_users_update')
      await executeQuery('DROP TRIGGER IF EXISTS tr_update_scheme_status')
      
      // Drop tables in reverse order
      for (const table of tables) {
        await executeQuery(`DROP TABLE IF EXISTS ${table}`)
      }
      
      console.log('✅ Schema rollback completed')
    } catch (error) {
      console.error('❌ Error rolling back schema:', error)
      throw error
    }
  }
}
