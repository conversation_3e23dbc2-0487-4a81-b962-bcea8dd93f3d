"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/inventory/items/route";
exports.ids = ["app/api/inventory/items/route"];
exports.modules = {

/***/ "mysql2/promise":
/*!*********************************!*\
  !*** external "mysql2/promise" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("mysql2/promise");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:crypto");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finventory%2Fitems%2Froute&page=%2Fapi%2Finventory%2Fitems%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finventory%2Fitems%2Froute.ts&appDir=C%3A%5Cproj%5Cjewellery-pro%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cjewellery-pro&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finventory%2Fitems%2Froute&page=%2Fapi%2Finventory%2Fitems%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finventory%2Fitems%2Froute.ts&appDir=C%3A%5Cproj%5Cjewellery-pro%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cjewellery-pro&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_jewellery_pro_app_api_inventory_items_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/inventory/items/route.ts */ \"(rsc)/./app/api/inventory/items/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/inventory/items/route\",\n        pathname: \"/api/inventory/items\",\n        filename: \"route\",\n        bundlePath: \"app/api/inventory/items/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\jewellery-pro\\\\app\\\\api\\\\inventory\\\\items\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_jewellery_pro_app_api_inventory_items_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/inventory/items/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finventory%2Fitems%2Froute&page=%2Fapi%2Finventory%2Fitems%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finventory%2Fitems%2Froute.ts&appDir=C%3A%5Cproj%5Cjewellery-pro%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cjewellery-pro&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/inventory/items/route.ts":
/*!******************************************!*\
  !*** ./app/api/inventory/items/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_config_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/config/database */ \"(rsc)/./lib/config/database.ts\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm-node/v4.js\");\n\n\n\n// GET - Fetch all inventory items\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"10\");\n        const search = searchParams.get(\"search\") || \"\";\n        const category = searchParams.get(\"category\") || \"\";\n        const status = searchParams.get(\"status\") || \"\";\n        const offset = (page - 1) * limit;\n        let whereClause = \"WHERE 1=1\";\n        const params = [];\n        if (search) {\n            whereClause += \" AND (name LIKE ? OR item_code LIKE ? OR description LIKE ?)\";\n            params.push(`%${search}%`, `%${search}%`, `%${search}%`);\n        }\n        if (category) {\n            whereClause += \" AND category = ?\";\n            params.push(category);\n        }\n        if (status) {\n            whereClause += \" AND status = ?\";\n            params.push(status);\n        }\n        // Get total count\n        const countQuery = `SELECT COUNT(*) as total FROM inventory_items ${whereClause}`;\n        const countResult = await (0,_lib_config_database__WEBPACK_IMPORTED_MODULE_1__.executeQuery)(countQuery, params);\n        const total = countResult[0].total;\n        // Get items with pagination\n        const itemsQuery = `\n      SELECT \n        id, item_code, name, category, subcategory, metal_type, purity,\n        weight, making_charges, stone_charges, other_charges, total_value,\n        current_stock, min_stock_level, max_stock_level, location,\n        supplier_id, description, status, created_at, updated_at\n      FROM inventory_items \n      ${whereClause}\n      ORDER BY created_at DESC\n      LIMIT ? OFFSET ?\n    `;\n        const items = await (0,_lib_config_database__WEBPACK_IMPORTED_MODULE_1__.executeQuery)(itemsQuery, [\n            ...params,\n            limit,\n            offset\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                items,\n                pagination: {\n                    page,\n                    limit,\n                    total,\n                    totalPages: Math.ceil(total / limit)\n                }\n            }\n        });\n    } catch (error) {\n        console.error(\"Get inventory items error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Failed to fetch inventory items\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - Create new inventory item\nasync function POST(request) {\n    try {\n        const data = await request.json();\n        const { name, category, subcategory, metal_type, purity, weight, making_charges = 0, stone_charges = 0, other_charges = 0, description, supplier_id, min_stock_level = 1, location, barcode } = data;\n        // Validate required fields\n        if (!name || !category || !metal_type || !purity || !weight) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Missing required fields\"\n            }, {\n                status: 400\n            });\n        }\n        const id = (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n        // Generate item code\n        const codePrefix = category.substring(0, 3).toUpperCase();\n        const timestamp = Date.now().toString().slice(-6);\n        const item_code = `${codePrefix}-${timestamp}`;\n        // Calculate total value (this would typically include current metal rates)\n        const total_value = parseFloat(making_charges) + parseFloat(stone_charges) + parseFloat(other_charges);\n        await (0,_lib_config_database__WEBPACK_IMPORTED_MODULE_1__.executeQuery)(`\n      INSERT INTO inventory_items (\n        id, item_code, name, category, subcategory, metal_type, purity,\n        weight, making_charges, stone_charges, other_charges, total_value,\n        current_stock, min_stock_level, location, supplier_id, description,\n        barcode, status, created_at, updated_at\n      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())\n    `, [\n            id,\n            item_code,\n            name,\n            category,\n            subcategory || null,\n            metal_type,\n            purity,\n            weight,\n            making_charges,\n            stone_charges,\n            other_charges,\n            total_value,\n            1,\n            min_stock_level,\n            location || null,\n            supplier_id || null,\n            description || null,\n            barcode || null,\n            \"active\"\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                id,\n                item_code\n            },\n            message: \"Item added successfully\"\n        });\n    } catch (error) {\n        console.error(\"Create inventory item error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Failed to create inventory item\"\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT - Update inventory item\nasync function PUT(request) {\n    try {\n        const data = await request.json();\n        const { id, ...updateData } = data;\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Item ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Build dynamic update query\n        const updateFields = [];\n        const params = [];\n        for (const [key, value] of Object.entries(updateData)){\n            if (value !== undefined && key !== \"id\") {\n                updateFields.push(`${key} = ?`);\n                params.push(value);\n            }\n        }\n        if (updateFields.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"No fields to update\"\n            }, {\n                status: 400\n            });\n        }\n        updateFields.push(\"updated_at = NOW()\");\n        params.push(id);\n        const query = `UPDATE inventory_items SET ${updateFields.join(\", \")} WHERE id = ?`;\n        await (0,_lib_config_database__WEBPACK_IMPORTED_MODULE_1__.executeQuery)(query, params);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"Item updated successfully\"\n        });\n    } catch (error) {\n        console.error(\"Update inventory item error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Failed to update inventory item\"\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - Delete inventory item\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get(\"id\");\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Item ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Soft delete - update status to 'deleted'\n        await (0,_lib_config_database__WEBPACK_IMPORTED_MODULE_1__.executeQuery)(\"UPDATE inventory_items SET status = ?, updated_at = NOW() WHERE id = ?\", [\n            \"deleted\",\n            id\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"Item deleted successfully\"\n        });\n    } catch (error) {\n        console.error(\"Delete inventory item error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Failed to delete inventory item\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/inventory/items/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/config/database.ts":
/*!********************************!*\
  !*** ./lib/config/database.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   closePool: () => (/* binding */ closePool),\n/* harmony export */   databaseConfig: () => (/* binding */ databaseConfig),\n/* harmony export */   executeDDLQuery: () => (/* binding */ executeDDLQuery),\n/* harmony export */   executeQuery: () => (/* binding */ executeQuery),\n/* harmony export */   executeTransaction: () => (/* binding */ executeTransaction),\n/* harmony export */   getPool: () => (/* binding */ getPool),\n/* harmony export */   testConnection: () => (/* binding */ testConnection)\n/* harmony export */ });\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mysql2/promise */ \"mysql2/promise\");\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mysql2_promise__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var dotenv__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dotenv */ \"(rsc)/./node_modules/dotenv/lib/main.js\");\n/* harmony import */ var dotenv__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dotenv__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Load environment variables\n(0,dotenv__WEBPACK_IMPORTED_MODULE_1__.config)();\nconst databaseConfig = {\n    host: process.env.DB_HOST || \"localhost\",\n    port: parseInt(process.env.DB_PORT || \"3306\"),\n    user: process.env.DB_USER || \"root\",\n    password: process.env.DB_PASSWORD || \"\",\n    database: process.env.DB_NAME || \"jewellery_pro_db\",\n    connectionLimit: 10,\n    acquireTimeout: 60000,\n    timeout: 60000,\n    reconnect: true,\n    charset: \"utf8mb4\"\n};\n// Create connection pool\nlet pool = null;\nfunction getPool() {\n    if (!pool) {\n        pool = mysql2_promise__WEBPACK_IMPORTED_MODULE_0___default().createPool({\n            ...databaseConfig,\n            namedPlaceholders: true,\n            supportBigNumbers: true,\n            bigNumberStrings: true,\n            dateStrings: false,\n            debug: \"development\" === \"development\",\n            multipleStatements: false,\n            timezone: \"+00:00\"\n        });\n        pool.on(\"connection\", (connection)=>{\n            console.log(\"New database connection established as id \" + connection.threadId);\n        });\n        pool.on(\"error\", (err)=>{\n            console.error(\"Database pool error:\", err);\n            if (err.code === \"PROTOCOL_CONNECTION_LOST\") {\n                console.log(\"Recreating database pool...\");\n                pool = null;\n                getPool();\n            }\n        });\n    }\n    return pool;\n}\n// Test database connection\nasync function testConnection() {\n    try {\n        const pool = getPool();\n        const connection = await pool.getConnection();\n        await connection.ping();\n        connection.release();\n        console.log(\"Database connection successful\");\n        return true;\n    } catch (error) {\n        console.error(\"Database connection failed:\", error);\n        return false;\n    }\n}\n// Execute query with error handling\nasync function executeQuery(query, params = []) {\n    const pool = getPool();\n    try {\n        const [results] = await pool.execute(query, params);\n        return results;\n    } catch (error) {\n        console.error(\"Query execution error:\", error);\n        console.error(\"Query:\", query);\n        console.error(\"Params:\", params);\n        throw error;\n    }\n}\n// Execute DDL query (for CREATE TRIGGER, etc.) using regular query instead of prepared statement\nasync function executeDDLQuery(query) {\n    const pool = getPool();\n    try {\n        const [results] = await pool.query(query);\n        return results;\n    } catch (error) {\n        console.error(\"DDL Query execution error:\", error);\n        console.error(\"Query:\", query);\n        throw error;\n    }\n}\n// Execute transaction\nasync function executeTransaction(callback) {\n    const pool = getPool();\n    const connection = await pool.getConnection();\n    try {\n        await connection.beginTransaction();\n        const result = await callback(connection);\n        await connection.commit();\n        return result;\n    } catch (error) {\n        await connection.rollback();\n        throw error;\n    } finally{\n        connection.release();\n    }\n}\n// Close all connections\nasync function closePool() {\n    if (pool) {\n        await pool.end();\n        pool = null;\n        console.log(\"Database pool closed\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/config/database.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/uuid","vendor-chunks/dotenv"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finventory%2Fitems%2Froute&page=%2Fapi%2Finventory%2Fitems%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finventory%2Fitems%2Froute.ts&appDir=C%3A%5Cproj%5Cjewellery-pro%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cjewellery-pro&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();