(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{6694:function(e,s,t){Promise.resolve().then(t.bind(t,7714))},7714:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return u}});var r=t(7437),a=t(2265),n=t(9376),i=t(9501),l=t(7769),o=t(2208),c=t(9090),d=t(212);function m(e){let{onSuccess:s}=e,[t,n]=(0,a.useState)(!1),[m,u]=(0,a.useState)(!1),{toast:x}=(0,d.pm)(),{register:h,handleSubmit:p,formState:{errors:j}}=(0,i.cI)(),f=async e=>{n(!0);try{let t=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),r=await t.json();r.success?(x({title:"Login Successful",description:"Welcome back, ".concat(r.data.user.first_name,"!")}),s(r.data.user)):x({title:"Login Failed",description:r.error||"Invalid credentials",variant:"destructive"})}catch(e){console.error("Login error:",e),x({title:"Login Failed",description:"An error occurred. Please try again.",variant:"destructive"})}finally{n(!1)}};return(0,r.jsxs)("form",{onSubmit:p(f),className:"space-y-4",children:[(0,r.jsxs)("div",{className:"form-group",children:[(0,r.jsx)("label",{htmlFor:"email",className:"form-label",children:"Email Address"}),(0,r.jsx)("input",{id:"email",type:"email",className:"form-input",placeholder:"Enter your email",...h("email",{required:"Email is required",pattern:{value:/^[^\s@]+@[^\s@]+\.[^\s@]+$/,message:"Please enter a valid email address"}})}),j.email&&(0,r.jsx)("p",{className:"form-error",children:j.email.message})]}),(0,r.jsxs)("div",{className:"form-group",children:[(0,r.jsx)("label",{htmlFor:"password",className:"form-label",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{id:"password",type:m?"text":"password",className:"form-input pr-10",placeholder:"Enter your password",...h("password",{required:"Password is required",minLength:{value:6,message:"Password must be at least 6 characters"}})}),(0,r.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>u(!m),children:m?(0,r.jsx)(l.Z,{className:"h-4 w-4 text-muted-foreground"}):(0,r.jsx)(o.Z,{className:"h-4 w-4 text-muted-foreground"})})]}),j.password&&(0,r.jsx)("p",{className:"form-error",children:j.password.message})]}),(0,r.jsx)("button",{type:"submit",disabled:t,className:"w-full btn btn-primary btn-md flex items-center justify-center gap-2",children:t?(0,r.jsx)("div",{className:"spinner"}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.Z,{className:"h-4 w-4"}),"Sign In"]})}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("p",{className:"text-xs text-muted-foreground text-center",children:"Quick Login:"}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-2",children:[(0,r.jsx)("button",{type:"button",className:"btn btn-outline btn-sm text-xs",onClick:()=>{let e=document.querySelector("form"),s=e.querySelector("#email"),t=e.querySelector("#password");s.value="<EMAIL>",t.value="admin123"},children:"Admin"}),(0,r.jsx)("button",{type:"button",className:"btn btn-outline btn-sm text-xs",onClick:()=>{let e=document.querySelector("form"),s=e.querySelector("#email"),t=e.querySelector("#password");s.value="<EMAIL>",t.value="manager123"},children:"Manager"}),(0,r.jsx)("button",{type:"button",className:"btn btn-outline btn-sm text-xs",onClick:()=>{let e=document.querySelector("form"),s=e.querySelector("#email"),t=e.querySelector("#password");s.value="<EMAIL>",t.value="staff123"},children:"Staff"})]})]})]})}function u(){let[e,s]=(0,a.useState)(null),[t,i]=(0,a.useState)(!0),l=(0,n.useRouter)();return((0,a.useEffect)(()=>{let e=localStorage.getItem("currentUser");if(e)try{let t=JSON.parse(e);s(t),l.push("/dashboard")}catch(e){console.error("Error parsing saved user:",e),localStorage.removeItem("currentUser")}i(!1)},[l]),t)?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"spinner"})}):e?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("h1",{className:"text-2xl font-bold mb-4",children:["Welcome back, ",e.first_name,"!"]}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:"Redirecting to dashboard..."}),(0,r.jsx)("div",{className:"spinner mx-auto"})]})}):(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gold-50 to-silver-50 dark:from-gray-900 dark:to-gray-800",children:(0,r.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,r.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"gold-gradient w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-foreground mb-2",children:"Jewellery Pro"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Complete Business Management System"})]}),(0,r.jsxs)("div",{className:"card",children:[(0,r.jsxs)("div",{className:"card-header",children:[(0,r.jsx)("h2",{className:"card-title text-center",children:"Sign In"}),(0,r.jsx)("p",{className:"card-description text-center",children:"Enter your credentials to access your account"})]}),(0,r.jsx)("div",{className:"card-content",children:(0,r.jsx)(m,{onSuccess:e=>{s(e),localStorage.setItem("currentUser",JSON.stringify(e)),l.push("/dashboard")}})})]}),(0,r.jsxs)("div",{className:"mt-6 p-4 bg-muted rounded-lg",children:[(0,r.jsx)("h3",{className:"text-sm font-medium mb-2",children:"Demo Credentials:"}),(0,r.jsxs)("div",{className:"text-xs text-muted-foreground space-y-1",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Admin:"})," <EMAIL> / admin123"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Manager:"})," <EMAIL> / manager123"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Staff:"})," <EMAIL> / staff123"]})]})]}),(0,r.jsxs)("div",{className:"mt-8 grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"text-center p-4 bg-card rounded-lg border",children:[(0,r.jsx)("div",{className:"w-8 h-8 mx-auto mb-2 text-primary",children:(0,r.jsx)("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"})})}),(0,r.jsx)("h3",{className:"text-sm font-medium",children:"Inventory"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Complete stock management"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-card rounded-lg border",children:[(0,r.jsx)("div",{className:"w-8 h-8 mx-auto mb-2 text-primary",children:(0,r.jsx)("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"})})}),(0,r.jsx)("h3",{className:"text-sm font-medium",children:"Sales"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"GST compliant billing"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-card rounded-lg border",children:[(0,r.jsx)("div",{className:"w-8 h-8 mx-auto mb-2 text-primary",children:(0,r.jsx)("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})})}),(0,r.jsx)("h3",{className:"text-sm font-medium",children:"Customers"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"CRM with KYC"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-card rounded-lg border",children:[(0,r.jsx)("div",{className:"w-8 h-8 mx-auto mb-2 text-primary",children:(0,r.jsx)("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})})}),(0,r.jsx)("h3",{className:"text-sm font-medium",children:"Reports"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Business analytics"})]})]})]})})})}},212:function(e,s,t){"use strict";t.d(s,{pm:function(){return u}});var r=t(2265);let a=0,n=new Map,i=e=>{if(n.has(e))return;let s=setTimeout(()=>{n.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,s)},l=(e,s)=>{switch(s.type){case"ADD_TOAST":return{...e,toasts:[s.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===s.toast.id?{...e,...s.toast}:e)};case"DISMISS_TOAST":{let{toastId:t}=s;return t?i(t):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===t||void 0===t?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===s.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==s.toastId)}}},o=[],c={toasts:[]};function d(e){c=l(c,e),o.forEach(e=>{e(c)})}function m(e){let{...s}=e,t=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>d({type:"DISMISS_TOAST",toastId:t});return d({type:"ADD_TOAST",toast:{...s,id:t,open:!0,onOpenChange:e=>{e||r()}}}),{id:t,dismiss:r,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function u(){let[e,s]=r.useState(c);return r.useEffect(()=>(o.push(s),()=>{let e=o.indexOf(s);e>-1&&o.splice(e,1)}),[e]),{...e,toast:m,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}}},function(e){e.O(0,[115,971,117,744],function(){return e(e.s=6694)}),_N_E=e.O()}]);