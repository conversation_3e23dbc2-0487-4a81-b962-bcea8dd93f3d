'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useToast } from '@/components/ui/simple-toast'
import {
  X,
  Save,
  Calculator,
  FileText,
  Printer,
  Share2
} from 'lucide-react'

interface GSTInvoiceDialogProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
}

interface InvoiceItem {
  id: string
  item_id: string
  item_name: string
  item_code: string
  hsn_code: string
  huid: string
  metal_type: string
  purity: string
  gross_weight: number
  stone_weight: number
  net_weight: number
  metal_rate: number
  making_charges: number
  stone_charges: number
  wastage_percent: number
  wastage_amount: number
  quantity: number
  taxable_value: number
  cgst_rate: number
  sgst_rate: number
  igst_rate: number
  cgst_amount: number
  sgst_amount: number
  igst_amount: number
  total_amount: number
}

interface InvoiceFormData {
  customer_id: string
  customer_name: string
  customer_phone: string
  customer_address: string
  customer_gstin: string
  invoice_type: 'B2B' | 'B2C'
  place_of_supply: string
  payment_method: string
  payment_terms: string
  notes: string
}

export function GSTInvoiceDialog({ isOpen, onClose, onSuccess }: GSTInvoiceDialogProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [invoiceItems, setInvoiceItems] = useState<InvoiceItem[]>([])
  const [customers, setCustomers] = useState<any[]>([])
  const [inventoryItems, setInventoryItems] = useState<any[]>([])
  const [metalRates, setMetalRates] = useState<any>({})
  const [selectedItem, setSelectedItem] = useState('')
  const { toast } = useToast()
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue
  } = useForm<InvoiceFormData>()

  const customerGSTIN = watch('customer_gstin')
  const placeOfSupply = watch('place_of_supply')

  useEffect(() => {
    if (isOpen) {
      loadCustomers()
      loadInventoryItems()
      loadMetalRates()
    }
  }, [isOpen])

  const loadCustomers = async () => {
    try {
      const response = await fetch('/api/customers')
      const result = await response.json()
      if (result.success) {
        setCustomers(result.data.customers)
      }
    } catch (error) {
      console.error('Failed to load customers:', error)
    }
  }

  const loadInventoryItems = async () => {
    try {
      const response = await fetch('/api/inventory/items')
      const result = await response.json()
      if (result.success) {
        setInventoryItems(result.data.items.filter((item: any) => item.current_stock > 0))
      }
    } catch (error) {
      console.error('Failed to load inventory items:', error)
    }
  }

  const loadMetalRates = async () => {
    try {
      const response = await fetch('/api/metal-rates/current')
      const result = await response.json()
      if (result.success) {
        setMetalRates(result.data)
      }
    } catch (error) {
      console.error('Failed to load metal rates:', error)
      // Default rates for demo
      setMetalRates({
        'gold_22k': 6200,
        'gold_18k': 4800,
        'silver': 75
      })
    }
  }

  const addItemToInvoice = () => {
    const item = inventoryItems.find(i => i.id === selectedItem)
    if (!item) return

    const metalRateKey = `${item.metal_type.toLowerCase()}_${item.purity.toLowerCase().replace('k', 'k')}`
    const currentRate = metalRates[metalRateKey] || 0

    const grossWeight = parseFloat(item.weight) || 0
    const stoneWeight = parseFloat(item.stone_weight) || 0
    const netWeight = grossWeight - stoneWeight
    const wastagePercent = 2 // Default 2% wastage
    const wastageAmount = (netWeight * currentRate * wastagePercent) / 100
    const makingCharges = parseFloat(item.making_charges) || 0
    const stoneCharges = parseFloat(item.stone_charges) || 0

    const taxableValue = (netWeight * currentRate) + wastageAmount + makingCharges + stoneCharges

    // GST rates based on item type
    const gstRate = item.metal_type === 'gold' ? 3 : 3 // 3% for gold/silver jewelry
    const isInterState = placeOfSupply !== 'Maharashtra' // Assuming business is in Maharashtra
    
    let cgstRate = 0, sgstRate = 0, igstRate = 0
    let cgstAmount = 0, sgstAmount = 0, igstAmount = 0

    if (isInterState) {
      igstRate = gstRate
      igstAmount = (taxableValue * igstRate) / 100
    } else {
      cgstRate = gstRate / 2
      sgstRate = gstRate / 2
      cgstAmount = (taxableValue * cgstRate) / 100
      sgstAmount = (taxableValue * sgstRate) / 100
    }

    const totalAmount = taxableValue + cgstAmount + sgstAmount + igstAmount

    const newItem: InvoiceItem = {
      id: Date.now().toString(),
      item_id: item.id,
      item_name: item.name,
      item_code: item.item_code,
      hsn_code: item.hsn_code || '71131900', // Default HSN for jewelry
      huid: item.huid || '',
      metal_type: item.metal_type,
      purity: item.purity,
      gross_weight: grossWeight,
      stone_weight: stoneWeight,
      net_weight: netWeight,
      metal_rate: currentRate,
      making_charges: makingCharges,
      stone_charges: stoneCharges,
      wastage_percent: wastagePercent,
      wastage_amount: wastageAmount,
      quantity: 1,
      taxable_value: taxableValue,
      cgst_rate: cgstRate,
      sgst_rate: sgstRate,
      igst_rate: igstRate,
      cgst_amount: cgstAmount,
      sgst_amount: sgstAmount,
      igst_amount: igstAmount,
      total_amount: totalAmount
    }

    setInvoiceItems([...invoiceItems, newItem])
    setSelectedItem('')
  }

  const removeInvoiceItem = (id: string) => {
    setInvoiceItems(items => items.filter(item => item.id !== id))
  }

  const updateItemQuantity = (id: string, quantity: number) => {
    setInvoiceItems(items => items.map(item => {
      if (item.id === id) {
        const updatedItem = { ...item, quantity }
        updatedItem.taxable_value = updatedItem.taxable_value * quantity
        updatedItem.cgst_amount = updatedItem.cgst_amount * quantity
        updatedItem.sgst_amount = updatedItem.sgst_amount * quantity
        updatedItem.igst_amount = updatedItem.igst_amount * quantity
        updatedItem.total_amount = updatedItem.total_amount * quantity
        return updatedItem
      }
      return item
    }))
  }

  const calculateTotals = () => {
    const subtotal = invoiceItems.reduce((sum, item) => sum + item.taxable_value, 0)
    const totalCGST = invoiceItems.reduce((sum, item) => sum + item.cgst_amount, 0)
    const totalSGST = invoiceItems.reduce((sum, item) => sum + item.sgst_amount, 0)
    const totalIGST = invoiceItems.reduce((sum, item) => sum + item.igst_amount, 0)
    const grandTotal = invoiceItems.reduce((sum, item) => sum + item.total_amount, 0)
    
    return { subtotal, totalCGST, totalSGST, totalIGST, grandTotal }
  }

  const onSubmit = async (data: InvoiceFormData) => {
    if (invoiceItems.length === 0) {
      toast({
        title: 'No Items',
        description: 'Please add at least one item to the invoice',
        variant: 'destructive',
      })
      return
    }

    setIsLoading(true)
    
    try {
      const { grandTotal, subtotal, totalCGST, totalSGST, totalIGST } = calculateTotals()
      
      const invoiceData = {
        ...data,
        items: invoiceItems,
        subtotal,
        cgst_amount: totalCGST,
        sgst_amount: totalSGST,
        igst_amount: totalIGST,
        total_amount: grandTotal,
        invoice_type: customerGSTIN ? 'B2B' : 'B2C'
      }

      const response = await fetch('/api/sales/gst-invoice', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(invoiceData),
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: 'GST Invoice Created',
          description: `Invoice ${result.data.invoice_number} generated successfully`,
        })
        reset()
        setInvoiceItems([])
        onSuccess()
        onClose()
      } else {
        toast({
          title: 'Failed to Create Invoice',
          description: result.error || 'An error occurred',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('Create invoice error:', error)
      toast({
        title: 'Error',
        description: 'Failed to create invoice. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (!isOpen) return null

  const { subtotal, totalCGST, totalSGST, totalIGST, grandTotal } = calculateTotals()

  return (
    <>
      {/* Overlay */}
      <div className="fixed inset-0 bg-black/50 z-50" onClick={onClose} />
      
      {/* Dialog */}
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-6xl max-h-[90vh] overflow-y-auto">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="w-5 h-5" />
                  <span>GST Invoice</span>
                </CardTitle>
                <CardDescription>Create GST-compliant invoice with HUID & HSN codes</CardDescription>
              </div>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="w-4 h-4" />
              </Button>
            </div>
          </CardHeader>
          
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Customer Information */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium">Customer Name *</label>
                  <input
                    {...register('customer_name', { required: 'Customer name is required' })}
                    className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="Customer name"
                  />
                  {errors.customer_name && (
                    <p className="text-red-500 text-xs mt-1">{errors.customer_name.message}</p>
                  )}
                </div>

                <div>
                  <label className="text-sm font-medium">Phone Number</label>
                  <input
                    {...register('customer_phone')}
                    className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="Phone number"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium">GSTIN (Optional)</label>
                  <input
                    {...register('customer_gstin')}
                    className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="22AAAAA0000A1Z5"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="text-sm font-medium">Address</label>
                  <textarea
                    {...register('customer_address')}
                    rows={2}
                    className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="Customer address"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium">Place of Supply *</label>
                  <select
                    {...register('place_of_supply', { required: 'Place of supply is required' })}
                    className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    <option value="">Select State</option>
                    <option value="Maharashtra">Maharashtra</option>
                    <option value="Gujarat">Gujarat</option>
                    <option value="Karnataka">Karnataka</option>
                    <option value="Tamil Nadu">Tamil Nadu</option>
                    <option value="Delhi">Delhi</option>
                    {/* Add more states */}
                  </select>
                  {errors.place_of_supply && (
                    <p className="text-red-500 text-xs mt-1">{errors.place_of_supply.message}</p>
                  )}
                </div>
              </div>

              {/* Add Items Section */}
              <div className="border rounded-lg p-4">
                <h3 className="text-lg font-medium mb-4">Invoice Items</h3>
                
                <div className="flex items-center space-x-2 mb-4">
                  <select
                    value={selectedItem}
                    onChange={(e) => setSelectedItem(e.target.value)}
                    className="flex-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    <option value="">Select an item to add</option>
                    {inventoryItems.map(item => (
                      <option key={item.id} value={item.id}>
                        {item.name} - {item.item_code} (Stock: {item.current_stock})
                      </option>
                    ))}
                  </select>
                  <Button type="button" onClick={addItemToInvoice} disabled={!selectedItem}>
                    <Calculator className="w-4 h-4 mr-2" />
                    Add Item
                  </Button>
                </div>

                {/* Items Table */}
                {invoiceItems.length > 0 && (
                  <div className="overflow-x-auto">
                    <table className="w-full border text-sm">
                      <thead>
                        <tr className="bg-muted">
                          <th className="text-left p-2">Item Details</th>
                          <th className="text-left p-2">HSN/HUID</th>
                          <th className="text-left p-2">Weight</th>
                          <th className="text-left p-2">Rate & Charges</th>
                          <th className="text-left p-2">Taxable Value</th>
                          <th className="text-left p-2">GST</th>
                          <th className="text-left p-2">Total</th>
                          <th className="text-left p-2">Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        {invoiceItems.map(item => (
                          <tr key={item.id} className="border-t">
                            <td className="p-2">
                              <div>
                                <div className="font-medium">{item.item_name}</div>
                                <div className="text-xs text-muted-foreground">{item.item_code}</div>
                                <div className="text-xs">{item.metal_type} {item.purity}</div>
                              </div>
                            </td>
                            <td className="p-2">
                              <div className="text-xs">
                                <div>HSN: {item.hsn_code}</div>
                                {item.huid && <div>HUID: {item.huid}</div>}
                              </div>
                            </td>
                            <td className="p-2">
                              <div className="text-xs">
                                <div>Gross: {item.gross_weight}g</div>
                                <div>Stone: {item.stone_weight}g</div>
                                <div>Net: {item.net_weight}g</div>
                              </div>
                            </td>
                            <td className="p-2">
                              <div className="text-xs">
                                <div>Rate: ₹{item.metal_rate}/g</div>
                                <div>Making: ₹{item.making_charges}</div>
                                <div>Stone: ₹{item.stone_charges}</div>
                                <div>Wastage: ₹{item.wastage_amount.toFixed(2)}</div>
                              </div>
                            </td>
                            <td className="p-2 font-medium">₹{item.taxable_value.toFixed(2)}</td>
                            <td className="p-2">
                              <div className="text-xs">
                                {item.cgst_amount > 0 && <div>CGST: ₹{item.cgst_amount.toFixed(2)}</div>}
                                {item.sgst_amount > 0 && <div>SGST: ₹{item.sgst_amount.toFixed(2)}</div>}
                                {item.igst_amount > 0 && <div>IGST: ₹{item.igst_amount.toFixed(2)}</div>}
                              </div>
                            </td>
                            <td className="p-2 font-bold">₹{item.total_amount.toFixed(2)}</td>
                            <td className="p-2">
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => removeInvoiceItem(item.id)}
                              >
                                <X className="w-4 h-4" />
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>

              {/* Invoice Summary */}
              {invoiceItems.length > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium">Payment Method *</label>
                      <select
                        {...register('payment_method', { required: 'Payment method is required' })}
                        className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                      >
                        <option value="">Select Payment Method</option>
                        <option value="cash">Cash</option>
                        <option value="card">Card</option>
                        <option value="upi">UPI</option>
                        <option value="bank_transfer">Bank Transfer</option>
                        <option value="cheque">Cheque</option>
                      </select>
                    </div>

                    <div>
                      <label className="text-sm font-medium">Notes</label>
                      <textarea
                        {...register('notes')}
                        rows={3}
                        className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                        placeholder="Additional notes..."
                      />
                    </div>
                  </div>

                  <div className="bg-muted p-4 rounded-lg">
                    <h4 className="font-medium mb-4">Invoice Summary</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Subtotal:</span>
                        <span>₹{subtotal.toFixed(2)}</span>
                      </div>
                      {totalCGST > 0 && (
                        <div className="flex justify-between">
                          <span>CGST:</span>
                          <span>₹{totalCGST.toFixed(2)}</span>
                        </div>
                      )}
                      {totalSGST > 0 && (
                        <div className="flex justify-between">
                          <span>SGST:</span>
                          <span>₹{totalSGST.toFixed(2)}</span>
                        </div>
                      )}
                      {totalIGST > 0 && (
                        <div className="flex justify-between">
                          <span>IGST:</span>
                          <span>₹{totalIGST.toFixed(2)}</span>
                        </div>
                      )}
                      <hr />
                      <div className="flex justify-between font-bold text-lg">
                        <span>Grand Total:</span>
                        <span>₹{grandTotal.toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex justify-end space-x-2 pt-4 border-t">
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading || invoiceItems.length === 0}>
                  {isLoading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                      Creating Invoice...
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      Create GST Invoice
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </>
  )
}
