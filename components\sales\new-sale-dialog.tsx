'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useToast } from '@/components/ui/simple-toast'
import {
  X,
  Save,
  Plus,
  Minus,
  ShoppingCart,
  Search,
  Calculator
} from 'lucide-react'

interface NewSaleDialogProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
}

interface SaleItem {
  id: string
  item_id: string
  item_name: string
  quantity: number
  unit_price: number
  discount: number
  total: number
}

interface SaleFormData {
  customer_id: string
  customer_name: string
  customer_phone: string
  payment_method: string
  payment_status: string
  discount_amount: number
  tax_amount: number
  notes: string
}

export function NewSaleDialog({ isOpen, onClose, onSuccess }: NewSaleDialogProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [saleItems, setSaleItems] = useState<SaleItem[]>([])
  const [inventoryItems, setInventoryItems] = useState<any[]>([])
  const [customers, setCustomers] = useState<any[]>([])
  const [selectedItem, setSelectedItem] = useState('')
  const { toast } = useToast()
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue
  } = useForm<SaleFormData>()

  useEffect(() => {
    if (isOpen) {
      loadInventoryItems()
      loadCustomers()
    }
  }, [isOpen])

  const loadInventoryItems = async () => {
    try {
      const response = await fetch('/api/inventory/items')
      const result = await response.json()
      if (result.success) {
        setInventoryItems(result.data.items.filter((item: any) => item.current_stock > 0))
      }
    } catch (error) {
      console.error('Failed to load inventory items:', error)
    }
  }

  const loadCustomers = async () => {
    try {
      const response = await fetch('/api/customers')
      const result = await response.json()
      if (result.success) {
        setCustomers(result.data.customers)
      }
    } catch (error) {
      console.error('Failed to load customers:', error)
    }
  }

  const addItemToSale = () => {
    const item = inventoryItems.find(i => i.id === selectedItem)
    if (!item) return

    const newItem: SaleItem = {
      id: Date.now().toString(),
      item_id: item.id,
      item_name: item.name,
      quantity: 1,
      unit_price: item.total_value || 0,
      discount: 0,
      total: item.total_value || 0
    }

    setSaleItems([...saleItems, newItem])
    setSelectedItem('')
  }

  const updateSaleItem = (id: string, field: keyof SaleItem, value: number) => {
    setSaleItems(items => items.map(item => {
      if (item.id === id) {
        const updated = { ...item, [field]: value }
        updated.total = (updated.quantity * updated.unit_price) - updated.discount
        return updated
      }
      return item
    }))
  }

  const removeSaleItem = (id: string) => {
    setSaleItems(items => items.filter(item => item.id !== id))
  }

  const calculateTotals = () => {
    const subtotal = saleItems.reduce((sum, item) => sum + item.total, 0)
    const discountAmount = watch('discount_amount') || 0
    const taxAmount = watch('tax_amount') || 0
    const grandTotal = subtotal - discountAmount + taxAmount
    
    return { subtotal, discountAmount, taxAmount, grandTotal }
  }

  const onSubmit = async (data: SaleFormData) => {
    if (saleItems.length === 0) {
      toast({
        title: 'No Items',
        description: 'Please add at least one item to the sale',
        variant: 'destructive',
      })
      return
    }

    setIsLoading(true)
    
    try {
      const { grandTotal } = calculateTotals()
      
      const saleData = {
        ...data,
        items: saleItems,
        total_amount: grandTotal,
        subtotal: calculateTotals().subtotal
      }

      const response = await fetch('/api/sales', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(saleData),
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: 'Sale Created Successfully',
          description: `Invoice ${result.data.invoice_number} has been generated`,
        })
        reset()
        setSaleItems([])
        onSuccess()
        onClose()
      } else {
        toast({
          title: 'Failed to Create Sale',
          description: result.error || 'An error occurred',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('Create sale error:', error)
      toast({
        title: 'Error',
        description: 'Failed to create sale. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (!isOpen) return null

  const { subtotal, discountAmount, taxAmount, grandTotal } = calculateTotals()

  return (
    <>
      {/* Overlay */}
      <div className="fixed inset-0 bg-black/50 z-50" onClick={onClose} />
      
      {/* Dialog */}
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center space-x-2">
                  <ShoppingCart className="w-5 h-5" />
                  <span>New Sale</span>
                </CardTitle>
                <CardDescription>Create a new sales transaction</CardDescription>
              </div>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="w-4 h-4" />
              </Button>
            </div>
          </CardHeader>
          
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Customer Information */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium">Customer Name *</label>
                  <input
                    {...register('customer_name', { required: 'Customer name is required' })}
                    className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="Customer name"
                  />
                  {errors.customer_name && (
                    <p className="text-red-500 text-xs mt-1">{errors.customer_name.message}</p>
                  )}
                </div>

                <div>
                  <label className="text-sm font-medium">Phone Number</label>
                  <input
                    {...register('customer_phone')}
                    className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="Phone number"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium">Payment Method *</label>
                  <select
                    {...register('payment_method', { required: 'Payment method is required' })}
                    className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    <option value="">Select Payment Method</option>
                    <option value="cash">Cash</option>
                    <option value="card">Card</option>
                    <option value="upi">UPI</option>
                    <option value="bank_transfer">Bank Transfer</option>
                  </select>
                  {errors.payment_method && (
                    <p className="text-red-500 text-xs mt-1">{errors.payment_method.message}</p>
                  )}
                </div>
              </div>

              {/* Add Items Section */}
              <div className="border rounded-lg p-4">
                <h3 className="text-lg font-medium mb-4">Sale Items</h3>
                
                <div className="flex items-center space-x-2 mb-4">
                  <select
                    value={selectedItem}
                    onChange={(e) => setSelectedItem(e.target.value)}
                    className="flex-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    <option value="">Select an item to add</option>
                    {inventoryItems.map(item => (
                      <option key={item.id} value={item.id}>
                        {item.name} - {item.item_code} (Stock: {item.current_stock})
                      </option>
                    ))}
                  </select>
                  <Button type="button" onClick={addItemToSale} disabled={!selectedItem}>
                    <Plus className="w-4 h-4 mr-2" />
                    Add Item
                  </Button>
                </div>

                {/* Items Table */}
                {saleItems.length > 0 && (
                  <div className="overflow-x-auto">
                    <table className="w-full border">
                      <thead>
                        <tr className="bg-muted">
                          <th className="text-left p-2">Item</th>
                          <th className="text-left p-2">Qty</th>
                          <th className="text-left p-2">Unit Price</th>
                          <th className="text-left p-2">Discount</th>
                          <th className="text-left p-2">Total</th>
                          <th className="text-left p-2">Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        {saleItems.map(item => (
                          <tr key={item.id} className="border-t">
                            <td className="p-2">{item.item_name}</td>
                            <td className="p-2">
                              <input
                                type="number"
                                min="1"
                                value={item.quantity}
                                onChange={(e) => updateSaleItem(item.id, 'quantity', parseInt(e.target.value) || 1)}
                                className="w-20 px-2 py-1 border rounded"
                              />
                            </td>
                            <td className="p-2">
                              <input
                                type="number"
                                step="0.01"
                                value={item.unit_price}
                                onChange={(e) => updateSaleItem(item.id, 'unit_price', parseFloat(e.target.value) || 0)}
                                className="w-24 px-2 py-1 border rounded"
                              />
                            </td>
                            <td className="p-2">
                              <input
                                type="number"
                                step="0.01"
                                value={item.discount}
                                onChange={(e) => updateSaleItem(item.id, 'discount', parseFloat(e.target.value) || 0)}
                                className="w-24 px-2 py-1 border rounded"
                              />
                            </td>
                            <td className="p-2 font-medium">₹{item.total.toFixed(2)}</td>
                            <td className="p-2">
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => removeSaleItem(item.id)}
                              >
                                <Minus className="w-4 h-4" />
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>

              {/* Totals and Payment */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Additional Discount (₹)</label>
                    <input
                      type="number"
                      step="0.01"
                      {...register('discount_amount')}
                      className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                      placeholder="0.00"
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium">Tax Amount (₹)</label>
                    <input
                      type="number"
                      step="0.01"
                      {...register('tax_amount')}
                      className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                      placeholder="0.00"
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium">Notes</label>
                    <textarea
                      {...register('notes')}
                      rows={3}
                      className="w-full mt-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                      placeholder="Additional notes..."
                    />
                  </div>
                </div>

                <div className="bg-muted p-4 rounded-lg">
                  <h4 className="font-medium mb-4">Sale Summary</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Subtotal:</span>
                      <span>₹{subtotal.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Discount:</span>
                      <span>-₹{discountAmount.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Tax:</span>
                      <span>₹{taxAmount.toFixed(2)}</span>
                    </div>
                    <hr />
                    <div className="flex justify-between font-bold text-lg">
                      <span>Total:</span>
                      <span>₹{grandTotal.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end space-x-2 pt-4 border-t">
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading || saleItems.length === 0}>
                  {isLoading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                      Creating Sale...
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      Create Sale
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </>
  )
}
