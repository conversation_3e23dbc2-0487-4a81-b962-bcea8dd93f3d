// Simple API test without database logs
const testEndpoints = [
  'http://localhost:3001/api/inventory/items',
  'http://localhost:3001/api/metal-rates/current',
  'http://localhost:3001/api/dashboard/summary',
  'http://localhost:3001/api/customers',
  'http://localhost:3001/api/sales',
  'http://localhost:3001/api/schemes',
  'http://localhost:3001/api/repairs'
]

async function testAPI(url) {
  try {
    const response = await fetch(url)
    const status = response.status
    const statusText = response.statusText
    
    if (response.ok) {
      console.log(`✅ ${url} → ${status} ${statusText}`)
    } else {
      console.log(`❌ ${url} → ${status} ${statusText}`)
    }
  } catch (error) {
    console.log(`❌ ${url} → ERROR: ${error.message}`)
  }
}

async function runTests() {
  console.log('🧪 Testing API endpoints...\n')
  
  for (const url of testEndpoints) {
    await testAPI(url)
  }
  
  console.log('\n🔐 Testing login endpoint...')
  try {
    const response = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    })
    
    if (response.ok) {
      console.log(`✅ Login API → ${response.status} ${response.statusText}`)
    } else {
      console.log(`❌ Login API → ${response.status} ${response.statusText}`)
    }
  } catch (error) {
    console.log(`❌ Login API → ERROR: ${error.message}`)
  }
  
  console.log('\n🎉 API testing complete!')
}

runTests()
