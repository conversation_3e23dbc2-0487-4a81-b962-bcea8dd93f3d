'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { User } from '@/lib/types'
import { DashboardLayout } from '@/components/dashboard/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { CreateEstimateDialog } from '@/components/estimation/create-estimate-dialog'
import { useToast } from '@/components/ui/simple-toast'
import { 
  Calculator,
  Plus,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Trash2,
  FileText,
  Clock,
  CheckCircle,
  DollarSign
} from 'lucide-react'

export default function EstimationPage() {
  const [currentUser, setCurrentUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [estimates, setEstimates] = useState<any[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('')
  const router = useRouter()
  const { toast } = useToast()

  const handleLogout = () => {
    localStorage.removeItem('currentUser')
    router.push('/')
  }

  useEffect(() => {
    const savedUser = localStorage.getItem('currentUser')
    if (savedUser) {
      try {
        const user = JSON.parse(savedUser)
        setCurrentUser(user)
        loadEstimates()
      } catch (error) {
        console.error('Error parsing saved user:', error)
        localStorage.removeItem('currentUser')
        router.push('/')
      }
    } else {
      router.push('/')
    }
    setIsLoading(false)
  }, [router])

  const loadEstimates = async () => {
    try {
      const params = new URLSearchParams()
      if (filterStatus) params.append('status', filterStatus)
      
      const response = await fetch(`/api/estimates?${params}`)
      const result = await response.json()
      
      if (result.success) {
        setEstimates(result.data.estimates || [])
      }
    } catch (error) {
      console.error('Failed to load estimates:', error)
      // Set sample data for demo
      setEstimates([
        {
          id: '1',
          estimate_number: 'EST-2024-001',
          customer_name: 'Priya Sharma',
          customer_phone: '+91 98765 43210',
          total_amount: 125000,
          status: 'pending',
          valid_till: '2024-12-15',
          created_at: '2024-12-08',
          items_count: 3
        },
        {
          id: '2',
          estimate_number: 'EST-2024-002',
          customer_name: 'Rajesh Kumar',
          customer_phone: '+91 87654 32109',
          total_amount: 85000,
          status: 'approved',
          valid_till: '2024-12-20',
          created_at: '2024-12-07',
          items_count: 2
        }
      ])
    }
  }

  const handleCreateSuccess = () => {
    loadEstimates()
    toast({
      title: 'Success',
      description: 'Estimate created successfully',
    })
  }

  const handleStatusUpdate = async (id: string, status: string) => {
    try {
      const response = await fetch('/api/estimates', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id, status }),
      })
      
      const result = await response.json()
      
      if (result.success) {
        loadEstimates()
        toast({
          title: 'Status Updated',
          description: `Estimate status updated to ${status}`,
        })
      }
    } catch (error) {
      console.error('Failed to update status:', error)
      toast({
        title: 'Error',
        description: 'Failed to update status',
        variant: 'destructive',
      })
    }
  }

  const convertToBill = async (estimateId: string) => {
    try {
      const response = await fetch(`/api/estimates/${estimateId}/convert-to-bill`, {
        method: 'POST',
      })
      
      const result = await response.json()
      
      if (result.success) {
        toast({
          title: 'Converted to Bill',
          description: `Bill ${result.data.invoice_number} created successfully`,
        })
        loadEstimates()
      }
    } catch (error) {
      console.error('Failed to convert to bill:', error)
      toast({
        title: 'Error',
        description: 'Failed to convert estimate to bill',
        variant: 'destructive',
      })
    }
  }

  if (isLoading) {
    return <div>Loading...</div>
  }

  // Filter estimates based on search and status
  const filteredEstimates = estimates.filter(estimate => {
    const matchesSearch = !searchTerm || 
      estimate.estimate_number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      estimate.customer_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      estimate.customer_phone?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = !filterStatus || estimate.status === filterStatus
    
    return matchesSearch && matchesStatus
  })

  return (
    <DashboardLayout currentUser={currentUser} onLogout={handleLogout}>
      <div>
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">Estimation</h1>
            <p className="text-muted-foreground">Create and manage jewelry estimates</p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              New Estimate
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Estimates</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{estimates.length}</div>
              <p className="text-xs text-muted-foreground">All time</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {estimates.filter(e => e.status === 'pending').length}
              </div>
              <p className="text-xs text-muted-foreground">Awaiting approval</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Approved</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {estimates.filter(e => e.status === 'approved').length}
              </div>
              <p className="text-xs text-muted-foreground">Ready to convert</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Value</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                ₹{estimates.reduce((sum, e) => sum + (e.total_amount || 0), 0).toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">All estimates</p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Search & Filter</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <input
                    type="text"
                    placeholder="Search estimates..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                  />
                </div>
              </div>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="">All Status</option>
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
                <option value="converted">Converted</option>
                <option value="expired">Expired</option>
              </select>
              <Button variant="outline">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Estimates Table */}
        <Card>
          <CardHeader>
            <CardTitle>Estimates</CardTitle>
            <CardDescription>Manage jewelry estimates and quotations</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4">Estimate #</th>
                    <th className="text-left py-3 px-4">Customer</th>
                    <th className="text-left py-3 px-4">Items</th>
                    <th className="text-left py-3 px-4">Amount</th>
                    <th className="text-left py-3 px-4">Status</th>
                    <th className="text-left py-3 px-4">Valid Till</th>
                    <th className="text-left py-3 px-4">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredEstimates.length > 0 ? (
                    filteredEstimates.map((estimate) => (
                      <tr key={estimate.id} className="border-b hover:bg-muted/50">
                        <td className="py-3 px-4 font-medium">{estimate.estimate_number}</td>
                        <td className="py-3 px-4">
                          <div>
                            <div className="font-medium">{estimate.customer_name}</div>
                            <div className="text-sm text-muted-foreground">{estimate.customer_phone}</div>
                          </div>
                        </td>
                        <td className="py-3 px-4">{estimate.items_count} items</td>
                        <td className="py-3 px-4 font-medium">₹{estimate.total_amount?.toLocaleString()}</td>
                        <td className="py-3 px-4">
                          <select
                            value={estimate.status}
                            onChange={(e) => handleStatusUpdate(estimate.id, e.target.value)}
                            className={`px-2 py-1 rounded text-xs border-0 ${
                              estimate.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                              estimate.status === 'approved' ? 'bg-green-100 text-green-800' :
                              estimate.status === 'rejected' ? 'bg-red-100 text-red-800' :
                              estimate.status === 'converted' ? 'bg-blue-100 text-blue-800' :
                              'bg-gray-100 text-gray-800'
                            }`}
                          >
                            <option value="pending">Pending</option>
                            <option value="approved">Approved</option>
                            <option value="rejected">Rejected</option>
                            <option value="converted">Converted</option>
                            <option value="expired">Expired</option>
                          </select>
                        </td>
                        <td className="py-3 px-4">
                          <div className="text-sm">
                            {new Date(estimate.valid_till).toLocaleDateString()}
                          </div>
                          {new Date(estimate.valid_till) < new Date() && (
                            <div className="text-xs text-red-600">Expired</div>
                          )}
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center space-x-2">
                            <Button variant="ghost" size="sm">
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Edit className="w-4 h-4" />
                            </Button>
                            {estimate.status === 'approved' && (
                              <Button 
                                variant="ghost" 
                                size="sm"
                                onClick={() => convertToBill(estimate.id)}
                                className="text-green-600"
                              >
                                <FileText className="w-4 h-4" />
                              </Button>
                            )}
                            <Button variant="ghost" size="sm">
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={7} className="py-8 text-center text-muted-foreground">
                        {estimates.length === 0 ? 'No estimates found' : 'No estimates match your search'}
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Create Estimate Dialog */}
        <CreateEstimateDialog
          isOpen={isCreateDialogOpen}
          onClose={() => setIsCreateDialogOpen(false)}
          onSuccess={handleCreateSuccess}
        />
      </div>
    </DashboardLayout>
  )
}
