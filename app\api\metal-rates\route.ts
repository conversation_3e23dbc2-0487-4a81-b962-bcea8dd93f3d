import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/config/database'
import { v4 as uuidv4 } from 'uuid'

// GET - Fetch metal rates
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const date = searchParams.get('date') || new Date().toISOString().split('T')[0]

    const ratesQuery = `
      SELECT 
        id, metal_type, purity, rate_per_gram, rate_per_10gram,
        effective_date, created_at, updated_at
      FROM metal_rates 
      WHERE effective_date <= ? 
      ORDER BY effective_date DESC, created_at DESC
    `
    
    const rates = await executeQuery<any[]>(ratesQuery, [date])

    // Get latest rate for each metal type and purity combination
    const latestRates = rates.reduce((acc: any[], rate) => {
      const key = `${rate.metal_type}_${rate.purity}`
      if (!acc.find(r => `${r.metal_type}_${r.purity}` === key)) {
        acc.push(rate)
      }
      return acc
    }, [])

    return NextResponse.json({
      success: true,
      data: {
        rates: latestRates,
        date
      }
    })

  } catch (error) {
    console.error('Get metal rates error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch metal rates' },
      { status: 500 }
    )
  }
}

// POST - Create/Update metal rates
export async function POST(request: NextRequest) {
  try {
    const data = await request.json()
    
    const {
      metal_type,
      purity,
      rate_per_gram,
      effective_date
    } = data

    // Validate required fields
    if (!metal_type || !purity || !rate_per_gram || !effective_date) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      )
    }

    const id = uuidv4()
    const rate_per_10gram = rate_per_gram * 10

    await executeQuery(`
      INSERT INTO metal_rates (
        id, metal_type, purity, rate_per_gram, rate_per_10gram,
        effective_date, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
    `, [
      id, metal_type, purity, rate_per_gram, rate_per_10gram, effective_date
    ])

    return NextResponse.json({
      success: true,
      data: { id },
      message: 'Metal rate updated successfully'
    })

  } catch (error) {
    console.error('Create metal rate error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update metal rate' },
      { status: 500 }
    )
  }
}

// PUT - Update metal rate
export async function PUT(request: NextRequest) {
  try {
    const data = await request.json()
    const { id, rate_per_gram } = data

    if (!id || !rate_per_gram) {
      return NextResponse.json(
        { success: false, error: 'ID and rate are required' },
        { status: 400 }
      )
    }

    const rate_per_10gram = rate_per_gram * 10

    await executeQuery(`
      UPDATE metal_rates 
      SET rate_per_gram = ?, rate_per_10gram = ?, updated_at = NOW()
      WHERE id = ?
    `, [rate_per_gram, rate_per_10gram, id])

    return NextResponse.json({
      success: true,
      message: 'Metal rate updated successfully'
    })

  } catch (error) {
    console.error('Update metal rate error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update metal rate' },
      { status: 500 }
    )
  }
}
