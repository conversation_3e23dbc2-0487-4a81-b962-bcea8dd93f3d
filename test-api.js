// Simple API test script
const testEndpoints = [
  'http://localhost:3001/api/inventory/items',
  'http://localhost:3001/api/metal-rates/current',
  'http://localhost:3001/api/dashboard/summary',
  'http://localhost:3001/api/customers',
  'http://localhost:3001/api/sales',
  'http://localhost:3001/api/schemes',
  'http://localhost:3001/api/repairs'
]

async function testAPI(url) {
  try {
    const response = await fetch(url)
    console.log(`${url} → ${response.status} ${response.statusText}`)
    if (!response.ok) {
      const errorText = await response.text()
      console.log(`  Error: ${errorText}`)
    }
  } catch (error) {
    console.log(`${url} → ERROR: ${error.message}`)
  }
}

async function runTests() {
  console.log('Testing API endpoints...\n')
  
  for (const url of testEndpoints) {
    await testAPI(url)
  }
  
  console.log('\nTesting login endpoint...')
  try {
    const response = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    })
    console.log(`Login API → ${response.status} ${response.statusText}`)
    if (response.ok) {
      const data = await response.json()
      console.log(`  Success: User ${data.user.name} logged in`)
    } else {
      const errorText = await response.text()
      console.log(`  Error: ${errorText}`)
    }
  } catch (error) {
    console.log(`Login API → ERROR: ${error.message}`)
  }
}

runTests()
