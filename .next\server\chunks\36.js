exports.id=36,exports.ids=[36],exports.modules={1070:(e,t,r)=>{"use strict";r.d(t,{JT:()=>i});var o=r(2418),n=r.n(o);(0,r(6636).config)();let s={host:process.env.DB_HOST||"localhost",port:parseInt(process.env.DB_PORT||"3306"),user:process.env.DB_USER||"root",password:process.env.DB_PASSWORD||"",database:process.env.DB_NAME||"jewellery_pro_db",connectionLimit:10,acquireTimeout:6e4,timeout:6e4,reconnect:!0,charset:"utf8mb4"},a=null;async function i(e,t=[]){let r=function e(){return a||((a=n().createPool({...s,namedPlaceholders:!0,supportBigNumbers:!0,bigNumberStrings:!0,dateStrings:!1,debug:!1,multipleStatements:!1,timezone:"+00:00"})).on("connection",e=>{console.log("New database connection established as id "+e.threadId)}),a.on("error",t=>{console.error("Database pool error:",t),"PROTOCOL_CONNECTION_LOST"===t.code&&(console.log("Recreating database pool..."),a=null,e())})),a}();try{let[o]=await r.execute(e,t);return o}catch(r){throw console.error("Query execution error:",r),console.error("Query:",e),console.error("Params:",t),r}}},6636:(e,t,r)=>{let o=r(2048),n=r(5315),s=r(9801),a=r(4770),i=r(1564).version,l=["\uD83D\uDD10 encrypt with Dotenvx: https://dotenvx.com","\uD83D\uDD10 prevent committing .env to code: https://dotenvx.com/precommit","\uD83D\uDD10 prevent building .env in docker: https://dotenvx.com/prebuild","\uD83D\uDCE1 observe env with Radar: https://dotenvx.com/radar","\uD83D\uDCE1 auto-backup env with Radar: https://dotenvx.com/radar","\uD83D\uDCE1 version env with Radar: https://dotenvx.com/radar","\uD83D\uDEE0️  run anywhere with `dotenvx run -- yourcommand`","⚙️  specify custom .env file path with { path: '/custom/path/.env' }","⚙️  enable debug logging with { debug: true }","⚙️  override existing env vars with { override: true }","⚙️  suppress all logs with { quiet: true }","⚙️  write to custom object with { processEnv: myObject }","⚙️  load multiple .env files with { path: ['.env.local', '.env'] }"];function c(e){return"string"==typeof e?!["false","0","no","off",""].includes(e.toLowerCase()):!!e}let p=/(?:^|^)\s*(?:export\s+)?([\w.-]+)(?:\s*=\s*?|:\s+?)(\s*'(?:\\'|[^'])*'|\s*"(?:\\"|[^"])*"|\s*`(?:\\`|[^`])*`|[^#\r\n]+)?\s*(?:#.*)?(?:$|$)/mg;function u(e){console.log(`[dotenv@${i}][DEBUG] ${e}`)}function d(e){console.log(`[dotenv@${i}] ${e}`)}function v(e){return e&&e.DOTENV_KEY&&e.DOTENV_KEY.length>0?e.DOTENV_KEY:process.env.DOTENV_KEY&&process.env.DOTENV_KEY.length>0?process.env.DOTENV_KEY:""}function f(e){let t=null;if(e&&e.path&&e.path.length>0){if(Array.isArray(e.path))for(let r of e.path)o.existsSync(r)&&(t=r.endsWith(".vault")?r:`${r}.vault`);else t=e.path.endsWith(".vault")?e.path:`${e.path}.vault`}else t=n.resolve(process.cwd(),".env.vault");return o.existsSync(t)?t:null}function g(e){return"~"===e[0]?n.join(s.homedir(),e.slice(1)):e}let h={configDotenv:function(e){let t;let r=n.resolve(process.cwd(),".env"),s="utf8",a=process.env;e&&null!=e.processEnv&&(a=e.processEnv);let i=c(a.DOTENV_CONFIG_DEBUG||e&&e.debug),p=c(a.DOTENV_CONFIG_QUIET||e&&e.quiet);e&&e.encoding?s=e.encoding:i&&u("No encoding is specified. UTF-8 is used by default");let v=[r];if(e&&e.path){if(Array.isArray(e.path))for(let t of(v=[],e.path))v.push(g(t));else v=[g(e.path)]}let f={};for(let r of v)try{let t=h.parse(o.readFileSync(r,{encoding:s}));h.populate(f,t,e)}catch(e){i&&u(`Failed to load ${r} ${e.message}`),t=e}let m=h.populate(a,f,e);if(i=c(a.DOTENV_CONFIG_DEBUG||i),p=c(a.DOTENV_CONFIG_QUIET||p),i||!p){var E;let e=Object.keys(m).length,r=[];for(let e of v)try{let t=n.relative(process.cwd(),e);r.push(t)}catch(r){i&&u(`Failed to load ${e} ${r.message}`),t=r}d(`injecting env (${e}) from ${r.join(",")} ${(E=`-- tip: ${l[Math.floor(Math.random()*l.length)]}`,process.stdout.isTTY?`\x1b[2m${E}\x1b[0m`:E)}`)}return t?{parsed:f,error:t}:{parsed:f}},_configVault:function(e){let t=c(process.env.DOTENV_CONFIG_DEBUG||e&&e.debug),r=c(process.env.DOTENV_CONFIG_QUIET||e&&e.quiet);(t||!r)&&d("Loading env from encrypted .env.vault");let o=h._parseVault(e),n=process.env;return e&&null!=e.processEnv&&(n=e.processEnv),h.populate(n,o,e),{parsed:o}},_parseVault:function(e){let t;let r=f(e=e||{});e.path=r;let o=h.configDotenv(e);if(!o.parsed){let e=Error(`MISSING_DATA: Cannot parse ${r} for an unknown reason`);throw e.code="MISSING_DATA",e}let n=v(e).split(","),s=n.length;for(let e=0;e<s;e++)try{let r=n[e].trim(),s=function(e,t){let r;try{r=new URL(t)}catch(e){if("ERR_INVALID_URL"===e.code){let e=Error("INVALID_DOTENV_KEY: Wrong format. Must be in valid uri format like dotenv://:<EMAIL>/vault/.env.vault?environment=development");throw e.code="INVALID_DOTENV_KEY",e}throw e}let o=r.password;if(!o){let e=Error("INVALID_DOTENV_KEY: Missing key part");throw e.code="INVALID_DOTENV_KEY",e}let n=r.searchParams.get("environment");if(!n){let e=Error("INVALID_DOTENV_KEY: Missing environment part");throw e.code="INVALID_DOTENV_KEY",e}let s=`DOTENV_VAULT_${n.toUpperCase()}`,a=e.parsed[s];if(!a){let e=Error(`NOT_FOUND_DOTENV_ENVIRONMENT: Cannot locate environment ${s} in your .env.vault file.`);throw e.code="NOT_FOUND_DOTENV_ENVIRONMENT",e}return{ciphertext:a,key:o}}(o,r);t=h.decrypt(s.ciphertext,s.key);break}catch(t){if(e+1>=s)throw t}return h.parse(t)},config:function(e){if(0===v(e).length)return h.configDotenv(e);let t=f(e);if(!t){var r;return r=`You set DOTENV_KEY but you are missing a .env.vault file at ${t}. Did you forget to build it?`,console.error(`[dotenv@${i}][WARN] ${r}`),h.configDotenv(e)}return h._configVault(e)},decrypt:function(e,t){let r=Buffer.from(t.slice(-64),"hex"),o=Buffer.from(e,"base64"),n=o.subarray(0,12),s=o.subarray(-16);o=o.subarray(12,-16);try{let e=a.createDecipheriv("aes-256-gcm",r,n);return e.setAuthTag(s),`${e.update(o)}${e.final()}`}catch(o){let e=o instanceof RangeError,t="Invalid key length"===o.message,r="Unsupported state or unable to authenticate data"===o.message;if(e||t){let e=Error("INVALID_DOTENV_KEY: It must be 64 characters long (or more)");throw e.code="INVALID_DOTENV_KEY",e}if(r){let e=Error("DECRYPTION_FAILED: Please check your DOTENV_KEY");throw e.code="DECRYPTION_FAILED",e}throw o}},parse:function(e){let t;let r={},o=e.toString();for(o=o.replace(/\r\n?/mg,"\n");null!=(t=p.exec(o));){let e=t[1],o=t[2]||"",n=(o=o.trim())[0];o=o.replace(/^(['"`])([\s\S]*)\1$/mg,"$2"),'"'===n&&(o=(o=o.replace(/\\n/g,"\n")).replace(/\\r/g,"\r")),r[e]=o}return r},populate:function(e,t,r={}){let o=!!(r&&r.debug),n=!!(r&&r.override),s={};if("object"!=typeof t){let e=Error("OBJECT_REQUIRED: Please check the processEnv argument being passed to populate");throw e.code="OBJECT_REQUIRED",e}for(let r of Object.keys(t))Object.prototype.hasOwnProperty.call(e,r)?(!0===n&&(e[r]=t[r],s[r]=t[r]),o&&(!0===n?u(`"${r}" is already defined and WAS overwritten`):u(`"${r}" is already defined and was NOT overwritten`))):(e[r]=t[r],s[r]=t[r]);return s}};e.exports.configDotenv=h.configDotenv,e.exports._configVault=h._configVault,e.exports._parseVault=h._parseVault,e.exports.config=h.config,e.exports.decrypt=h.decrypt,e.exports.parse=h.parse,e.exports.populate=h.populate,e.exports=h},9576:(e,t,r)=>{"use strict";r.d(t,{Z:()=>c});var o=r(6005),n=r.n(o);let s={randomUUID:n().randomUUID},a=new Uint8Array(256),i=a.length,l=[];for(let e=0;e<256;++e)l.push((e+256).toString(16).slice(1));let c=function(e,t,r){if(s.randomUUID&&!t&&!e)return s.randomUUID();let o=(e=e||{}).random||(e.rng||function(){return i>a.length-16&&(n().randomFillSync(a),i=0),a.slice(i,i+=16)})();if(o[6]=15&o[6]|64,o[8]=63&o[8]|128,t){r=r||0;for(let e=0;e<16;++e)t[r+e]=o[e];return t}return function(e,t=0){return(l[e[t+0]]+l[e[t+1]]+l[e[t+2]]+l[e[t+3]]+"-"+l[e[t+4]]+l[e[t+5]]+"-"+l[e[t+6]]+l[e[t+7]]+"-"+l[e[t+8]]+l[e[t+9]]+"-"+l[e[t+10]]+l[e[t+11]]+l[e[t+12]]+l[e[t+13]]+l[e[t+14]]+l[e[t+15]]).toLowerCase()}(o)}},1564:e=>{"use strict";e.exports=JSON.parse('{"name":"dotenv","version":"17.2.1","description":"Loads environment variables from .env file","main":"lib/main.js","types":"lib/main.d.ts","exports":{".":{"types":"./lib/main.d.ts","require":"./lib/main.js","default":"./lib/main.js"},"./config":"./config.js","./config.js":"./config.js","./lib/env-options":"./lib/env-options.js","./lib/env-options.js":"./lib/env-options.js","./lib/cli-options":"./lib/cli-options.js","./lib/cli-options.js":"./lib/cli-options.js","./package.json":"./package.json"},"scripts":{"dts-check":"tsc --project tests/types/tsconfig.json","lint":"standard","pretest":"npm run lint && npm run dts-check","test":"tap run --allow-empty-coverage --disable-coverage --timeout=60000","test:coverage":"tap run --show-full-coverage --timeout=60000 --coverage-report=text --coverage-report=lcov","prerelease":"npm test","release":"standard-version"},"repository":{"type":"git","url":"git://github.com/motdotla/dotenv.git"},"homepage":"https://github.com/motdotla/dotenv#readme","funding":"https://dotenvx.com","keywords":["dotenv","env",".env","environment","variables","config","settings"],"readmeFilename":"README.md","license":"BSD-2-Clause","devDependencies":{"@types/node":"^18.11.3","decache":"^4.6.2","sinon":"^14.0.1","standard":"^17.0.0","standard-version":"^9.5.0","tap":"^19.2.0","typescript":"^4.8.4"},"engines":{"node":">=12"},"browser":{"fs":false}}')}};